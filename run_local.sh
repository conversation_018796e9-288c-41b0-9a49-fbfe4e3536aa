#!/bin/bash
# -*- coding: utf-8 -*-
# 本地开发环境启动脚本
# 基于 run_api.py 创建的本地启动脚本

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 默认配置
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8080"
DEFAULT_WORKERS="4"
DEFAULT_LOG_LEVEL="info"
ENVIRONMENT="local"

# 解析命令行参数
HOST=$DEFAULT_HOST
PORT=$DEFAULT_PORT
WORKERS=$DEFAULT_WORKERS
LOG_LEVEL=$DEFAULT_LOG_LEVEL
RELOAD=true

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST        服务主机地址 (默认: $DEFAULT_HOST)"
    echo "  -p, --port PORT        服务端口 (默认: $DEFAULT_PORT)"
    echo "  -w, --workers WORKERS  工作进程数量 (默认: $DEFAULT_WORKERS)"
    echo "  -l, --log-level LEVEL  日志级别 (默认: $DEFAULT_LOG_LEVEL)"
    echo "  --no-reload            禁用热重载"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认配置启动"
    echo "  $0 -p 8080             # 在端口8081启动"
    echo "  $0 --no-reload         # 禁用热重载启动"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --no-reload)
            RELOAD=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done


# 检查环境配置文件
check_env_config() {
    env_file=".env.${ENVIRONMENT}"
    
    if [ -f "$env_file" ]; then
        print_success "发现环境配置文件: $env_file"
        export ENVIRONMENT=$ENVIRONMENT
    elif [ -f ".env" ]; then
        print_info "使用默认环境配置文件: .env"
        export ENVIRONMENT=$ENVIRONMENT
    else
        print_warning "未找到环境配置文件: $env_file 或 .env"
    fi
}

# 检查API应用文件
check_api_files() {
    if [ ! -f "run_api.py" ]; then
        print_error "未找到 run_api.py 文件"
        exit 1
    fi
    
    if [ ! -f "api/app.py" ]; then
        print_error "未找到 api/app.py 文件"
        exit 1
    fi
    
    print_success "API文件检查通过"
}

# 启动服务
start_service() {
    print_info "启动API服务..."
    print_info "配置信息:"
    print_info "  主机地址: $HOST"
    print_info "  端口: $PORT"
    print_info "  工作进程: $WORKERS"
    print_info "  日志级别: $LOG_LEVEL"
    print_info "  热重载: $RELOAD"
    print_info "  环境: $ENVIRONMENT"
    
    # 构建启动命令
    cmd_args="--host $HOST --port $PORT --workers $WORKERS --log-level $LOG_LEVEL"
    
    if [ "$RELOAD" = true ]; then
        cmd_args="$cmd_args --reload"
    fi
    
    print_info "执行命令: python3 run_api.py $cmd_args"
    print_success "服务启动中..."
    print_info "访问地址: http://$HOST:$PORT"
    print_info "API文档: http://$HOST:$PORT/docs"
    print_info "按 Ctrl+C 停止服务"
    
    # 设置环境变量并启动服务
    export ENVIRONMENT=$ENVIRONMENT
    python3 run_api.py $cmd_args
}

# 主函数
main() {
    print_info "=== IPD问答助手本地启动脚本 ==="
    
    check_api_files
    check_env_config
    
    # 启动服务
    start_service
}

# 捕获中断信号
trap 'print_info "正在停止服务..."; exit 0' INT TERM

# 执行主函数
main "$@"