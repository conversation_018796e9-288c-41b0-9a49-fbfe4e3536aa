from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.personal_qa_prompt import (
    personal_qa_rerank_prompt,
    personal_qa_sys_prompt,
    personal_qa_user_prompt,
    personal_qa_strict_empty_sys_prompt,
    personal_qa_strict_nonempty_sys_prompt,
    personal_qa_common_empty_sys_prompt,
    personal_qa_common_nonempty_sys_prompt,
    personal_qa_strict_empty_user_prompt,
    personal_qa_strict_nonempty_user_prompt,
    personal_qa_common_empty_user_prompt,
    personal_qa_common_nonempty_user_prompt
)
from services.search_service import SearchService
from services.rerank_service import RerankService
from utils.token_limiter import CharacterLimiter
from config.model_config import get_model_max_input_chars

from loguru import logger
from config.logging_config import configure_logging
from config.personal_search_config import PERSO<PERSON>L_SEARCH_MODEL_CONFIG, PERSONAL_RERANK_MODEL_CONFIG, PERSONAL_SEARCH_COLLECTIONS
configure_logging()
import json
import asyncio


class PERSONALQA:
    """个人知识库问答类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化个人知识库问答实例

        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b", "qwen3_235b_2507")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化搜索服务
        self.search_service = SearchService(config=PERSONAL_SEARCH_MODEL_CONFIG, request_id=request_id, knowledge="personal")
        # 实例化重排服务
        self.rerank_service = RerankService(config=PERSONAL_RERANK_MODEL_CONFIG, request_id=request_id)
        # 根据模型ID获取最大输入字数并实例化字符数限制器
        max_input_chars = get_model_max_input_chars(model_id)
        self.char_limiter = CharacterLimiter(max_chars=max_input_chars)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"PERSONALQA instance initialized with model_id: {model_id}, 最大输入字数: {max_input_chars}")

    def format_retrieved_docs(self, retrieved_docs: List[Any]) -> List[Any]:
        """格式化检索到的文档"""
        formated_retrieved_docs = []
        for doc in retrieved_docs:
            # print(f"doc: {doc}")
            data = {
                "title": "",
                "content": "",
                "docName": "",
                "docUrl": "",
                "sheetName": "",
                "owner": "",
                "update_time": "",
                "publish_time": "",
                "doc_type": "",
                "project_area": ""
            }
            docUrl1 = doc.get("metadata_json", {}).get("doc_url", "")
            docUrl2 = doc.get("metadata_json", {}).get("url", "")
            docUrl = docUrl1 if docUrl1 else docUrl2
            docUrl = str(docUrl)
            # print(f"docUrl: {docUrl}")
            title1 = doc.get("metadata_json", {}).get("doc_name", "")
            title2 = doc.get("metadata_json", {}).get("title", "")
            # print(f"title1: {title1}")
            # print(f"title2: {title2}")
            title = title1 if title1 else title2
            title = str(title)
            # print(f"title: {title}")
            data.update({"title": title})
            data.update({"content": doc.get("content", "")})
            data.update({"docName": title})
            data.update({"docUrl": docUrl})
            data.update({"owner": doc.get("metadata_json", {}).get("owner", "")})
            data.update({"update_time": doc.get("metadata_json", {}).get("update_time", "")})
            data.update({"publish_time": doc.get("metadata_json", {}).get("publish_time", "")})
            data.update({"project_area": doc.get("metadata_json", {}).get("project_area", "")})
            data.update({"doc_type": doc.get("metadata_json", {}).get("doc_type", "")})
            docType = doc.get("metadata_json", {}).get("doc_type", "")
            if "xiaomi.f.mioffice.cn"in docUrl and docType in ["doc", "sheet"]:
                sheetName = doc.get("metadata_json", {}).get("Header 2", "")
                data.update({"sheetName": sheetName})
            formated_retrieved_docs.append(data)
            # print(f"data: {data}")
        return formated_retrieved_docs

    def _get_provider(self, enable_thinking: bool = True):
        """根据enable_thinking参数获取合适的provider"""
        return get_llm_provider(self.model_id, self.request_id, enable_thinking)
    
    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = None, min_score: float = None, create_user: str = None, use_reranker: bool = True):
        """检索单个库并重排，不做top_r过滤"""
        self.logger.info(f"检索个人知识库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}, use_reranker: {use_reranker}")
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name,
            create_user=create_user
        )
        if error or not search_results:
            return []
        self.logger.info(f"个人知识库 {collection_name} 检索到知识: {len(search_results)} 条")

        if use_reranker:
            reranked_docs = await self.rerank_service.rerank_with_prompt(
                query=query,
                documents=search_results,
                instruction=personal_qa_rerank_prompt,
                top_r=top_k,
                min_score=min_score
            )
            self.logger.info(f"个人知识库 {collection_name} 重排后知识: {len(reranked_docs)} 条")
        else:
            self.logger.info(f"跳过重排，直接使用原始检索结果")
            reranked_docs = search_results
        # 格式化重排后的文档
        format_reranked_docs = self.format_retrieved_docs(reranked_docs)
        self.logger.info(f"库 {collection_name} 格式化完成，共获取到 {len(format_reranked_docs)} 个文档")
        return format_reranked_docs

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, top_k: int = None, top_r: int = None, min_score: float = None, create_user: str = None, use_reranker: bool = True):
        """异步检索所有个人知识库collection并重排，最后统一排序取top_r"""
        tasks = []
        for collection in PERSONAL_SEARCH_COLLECTIONS:
            collection_name = collection.get("collection_name")
            if collection_name:
                tasks.append(self._retrieve_knowledge(query, user_id, collection_name, top_k=top_k, min_score=min_score, create_user=create_user, use_reranker=use_reranker))
        # 并发检索和重排
        all_results = await asyncio.gather(*tasks)
        # 合并所有库的重排结果
        merged_docs = [doc for docs in all_results for doc in docs]
        # 按score降序排序
        merged_docs.sort(key=lambda x: x.get("score", 0), reverse=True)
        # 只取前top_r
        if top_r is not None:
            merged_docs = merged_docs[:top_r]
        self.logger.info(f"所有个人知识库合并后知识: {len(merged_docs)} 条，top_r: {top_r}")
        return merged_docs

    def format_knowledge(self, rerank_res: List[Dict]) -> tuple:
        """格式化知识为字符串"""
        if not rerank_res:
            return "", []
        formatted_docs = []
        for i, doc in enumerate(rerank_res):
            formatted_doc = f"文档序号: {i+1}\n"
            formatted_doc += f"标题: {doc.get('title', '')}\n"
            formatted_doc += f"内容: {doc.get('content', '')}\n"
            # formatted_doc += f"文档名称: {doc.get('docName', '')}\n"
            formatted_doc += f"章节名称: {doc.get('sheetName', '')}\n"
            # formatted_doc += f"文档链接: {doc.get('docUrl', '')}\n"
            # formatted_doc += f"作者: {doc.get('owner', '')}\n"
            formatted_doc += f"更新时间: {doc.get('update_time', '')}\n"
            # formatted_doc += f"文档类型: {doc.get('doc_type', '')}\n"
            formatted_docs.append(formatted_doc)
            rerank_res[i]["refNum"] = i+1
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), rerank_res
    
    def _build_messages(self, query: str, history: List[Dict], knowledge: str, mode: str = "common") -> List[Dict[str, str]]:
        """构建消息列表"""
        is_knowledge_empty = not knowledge or knowledge.strip() == ""

        if mode == "strict":
            if is_knowledge_empty:
                self.logger.info(f"strict模式下, 知识为空")
                sys_prompt = personal_qa_strict_empty_sys_prompt
                user_prompt = personal_qa_strict_empty_user_prompt
            else:
                self.logger.info(f"strict模式下, 知识不为空")
                sys_prompt = personal_qa_strict_nonempty_sys_prompt
                user_prompt = personal_qa_strict_nonempty_user_prompt
        else:  # common mode
            if is_knowledge_empty:
                self.logger.info(f"common模式下, 知识为空")
                sys_prompt = personal_qa_common_empty_sys_prompt
                user_prompt = personal_qa_common_empty_user_prompt
            else:
                self.logger.info(f"common模式下, 知识不为空")
                sys_prompt = personal_qa_common_nonempty_sys_prompt
                user_prompt = personal_qa_common_nonempty_user_prompt

        messages = [{"role": "system", "content": sys_prompt}]
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        formatted_query = user_prompt.replace("{{query}}", query).replace("{{body}}", knowledge)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        top_k: int = None,
        top_r: int = None,
        min_score: float = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        mode: str = "common",
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        create_user: Optional[str] = None,
        use_reranker: bool = True,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        个人知识库流式问答生成
        """
        if history is None:
            self.logger.info(f"历史对话为空，开始新对话")
            history = []
        self.logger.info(f"历史对话条数: {len(history)}")
        # 检索所有个人知识库并重排
        rerank_res = await self._retrieve_and_rerank_all_collections(query, user_id, top_k=top_k, top_r=top_r, min_score=min_score, create_user=create_user, use_reranker=use_reranker)
        knowledge, rerank_res = self.format_knowledge(rerank_res)
        self.logger.info(f"个人知识检索完成")
        # 先发送引用信息
        yield {"type": "reference", "content": json.dumps(rerank_res, ensure_ascii=False), "role": "", "finish_reason": ""}
        # print(f"检索到的知识: {rerank_res}")
        # 应用字符数长度限制
        limited_query, limited_history, limited_knowledge = self.char_limiter.limit_messages_for_rag_qa(
            query, history, knowledge
        )

        # 构建消息
        messages = self._build_messages(limited_query, limited_history, limited_knowledge, mode)
        self.logger.info(f"开始调用模型服务")

        # 模型服务的流式调用
        provider = self._get_provider(enable_thinking)
        async for chunk in provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            temperature=temperature,
            top_p=top_p,
            **kwargs
        ):
            yield chunk