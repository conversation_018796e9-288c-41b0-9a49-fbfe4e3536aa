from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.llm_qa_prompt import system_prompt, user_query, system_prompt_235B_2503, web_search_system_prompt, user_query_with_search
from services.websearch_service import get_websearch_service
from utils.token_limiter import CharacterLimiter
from config.model_config import get_model_max_input_chars
import json
from loguru import logger
from config.logging_config import configure_logging 
configure_logging()

class LLMQA:
    """LLM问答基础类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化问答实例

        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b", "qwen3_235b_2507")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 根据模型ID获取最大输入字数并实例化字符数限制器
        max_input_chars = get_model_max_input_chars(model_id)
        self.char_limiter = CharacterLimiter(max_chars=max_input_chars)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"初始化LLMQA，模型: {model_id}, 最大输入字数: {max_input_chars}")

    def _get_provider(self, enable_thinking: bool = True):
        """根据enable_thinking参数获取合适的provider"""
        return get_llm_provider(self.model_id, self.request_id, enable_thinking)
    
    def format_knowledge(self, reranked_docs):
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"检索信息序号: {i+1}\n"
            formatted_doc += f"检索信息标题: {doc.get('title', '')}\n"
            formatted_doc += f"检索信息内容: {doc.get('content', '')}\n"
            # formatted_doc += f"章节名称: {doc.get('sheetName', '')}\n"
            # formatted_doc += f"检索信息更新时间: {doc.get('update_time', '')}\n"
            formatted_docs.append(formatted_doc)
            reranked_docs[i]["refNum"] = i+1
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def _build_messages(self, query: str, history: List[Dict], web_search_context: str = "") -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        self.logger.info(f"构建消息列表: {query}")
        
        # 选择合适的系统prompt
        if web_search_context:
            # 如果有联网搜索上下文，使用联网搜索专用的系统prompt
            system_msg = web_search_system_prompt
        else:
            # 根据模型选择基础系统prompt
            system_msg = system_prompt_235B_2503 if self.model_id == "qwen3_235b_2507" else system_prompt
            
        messages = [{"role": "system", "content": system_msg}]
        
        # print(f"history: {history}")
        # 适配新格式history
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        
        # 构建用户查询，如果有搜索上下文则添加
        if web_search_context:
            formatted_query = user_query_with_search.replace("{query}", query).replace("{web_search_context}", web_search_context)
        else:
            formatted_query = user_query.replace("{query}", query)
            
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    async def generate(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        enable_web_search: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式问答生成
        """
        # 如果启用联网搜索，先进行搜索
        web_search_context = ""
        if enable_web_search:
            try:
                websearch_service = get_websearch_service()
                search_result = await websearch_service.search(query, user_id)
                if 'webPages' in search_result:
                    # summaries = websearch_service.extract_summaries(search_result)
                    # web_search_context = websearch_service.format_search_context(summaries)
                    web_search_context = websearch_service.format_results(search_result)
                    self.logger.info(f"联网搜索成功，获取上下文长度: {len(web_search_context)}")
                    print(f"联网搜索成功，获取上下文: {web_search_context}")
                else:
                    self.logger.warning(f"联网搜索失败: {search_result.get('error', '未知错误')}")
            except Exception as e:
                self.logger.error(f"联网搜索异常: {str(e)}")

        # 应用字符数长度限制
        limited_query, limited_history, limited_web_context = self.char_limiter.limit_messages_for_llm_qa(
            query, history, web_search_context
        )

        messages = self._build_messages(limited_query, limited_history, limited_web_context)
        provider = self._get_provider(enable_thinking)
        return await provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        enable_web_search: bool = False,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        # 如果启用联网搜索，先进行搜索
        knowledge = ""
        if enable_web_search:
            try:
                websearch_service = get_websearch_service()
                search_result = await websearch_service.search(query, user_id)
                if 'webPages' in search_result:
                    # summaries = websearch_service.extract_summaries(search_result)
                    # web_search_context = websearch_service.format_search_context(summaries)
                    web_search_context = websearch_service.format_results(search_result)
                    self.logger.info(f"联网搜索成功，获取上下文长度: {len(web_search_context)}")
                    # print(f"联网搜索成功，获取上下文: {web_search_context}")
                else:
                    self.logger.warning(f"联网搜索失败: {search_result.get('error', '未知错误')}")
            except Exception as e:
                self.logger.error(f"联网搜索异常: {str(e)}")

            knowledge, web_search_context = self.format_knowledge(web_search_context)
            self.logger.info(f"知识格式化完成，共: {len(web_search_context)}")
            # print(f"联网搜索成功，获取上下文\n: {knowledge}")
            yield {"type": "reference", "content": json.dumps(web_search_context, ensure_ascii=False), "role": "", "finish_reason": ""}

        # 应用字符数长度限制
        limited_query, limited_history, limited_knowledge = self.char_limiter.limit_messages_for_llm_qa(
            query, history, knowledge
        )

        # 模型服务的流式调用
        messages = self._build_messages(limited_query, limited_history, limited_knowledge)
        self.logger.info(f"对话消息列表: {json.dumps(messages,ensure_ascii=False)[:500]}...")
        # print(f"message: {messages}")
        provider = self._get_provider(enable_thinking)
        async for chunk in provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk
                
