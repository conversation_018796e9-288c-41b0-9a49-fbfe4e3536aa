#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题改写服务演示脚本

展示QueryRewriteService的主要功能：
1. 代码提取和识别
2. 数字变体生成
3. 查询改写
4. 批量处理
"""

import sys
import os
import re
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 简化版的QueryRewriteService，避免日志配置问题
class SimpleQueryRewriteService:
    """简化版问题改写服务类，用于演示"""

    def __init__(self):
        # 定义代码模式
        self.CODE_PREFIX_PATTERN = r'(?:错误代码|故障代码|故障编码|代码|编码|编号|序列|型号|版本|错误|故障|报警|报警代码|报错|报错代码|Fault|fault|F)'
        self.CODE_VALUE_PATTERN = r'(?:\d+|[A-Za-z]+\d+)'
        self.CODE_PATTERN = re.compile(f'({self.CODE_PREFIX_PATTERN}[ \t]*{self.CODE_VALUE_PATTERN})')
        self.STANDALONE_CODE_PATTERN = re.compile(r'([A-Za-z]+[0-9]+[A-Za-z0-9-_]*|[0-9]+[A-Za-z]+[A-Za-z0-9-_]*|[A-Za-z0-9]+[-_][A-Za-z0-9]+(?:[-_][A-Za-z0-9]+)*)')
        self._CODE_VALUE_REGEX = re.compile(f'{self.CODE_PREFIX_PATTERN}[ \t]*({self.CODE_VALUE_PATTERN})')

        # 停用词集合
        self.STOPWORDS = {
            "的", "了", "和", "是", "在", "我", "有", "你", "他", "她", "它", "们", "这", "那", "哪",
            "什么", "怎么", "如何", "为什么", "啊", "吗", "呢", "吧", "啦", "呀", "哦", "哈", "嗯",
            " ", "\t", "\n", "\r", "\f", "\v",
            ".", ",", ";", ":", "?", "!", "，", "。", "；", "：", "？", "！",
            "、", "…", "...", "\"", "'", "(", ")", "（", "）",
            "[", "]", "【", "】", "{", "}", "《", "》", "<", ">",
            "+", "-", "*", "/", "=", "%", "$", "#", "@", "&", "|", "\\", "^", "~"
        }

    def extract_and_process_codes(self, text: str) -> List[str]:
        """提取并处理文本中的独立字母数字混合代码"""
        matches = self.STANDALONE_CODE_PATTERN.findall(text)
        unique_codes = {match for match in matches if match}
        return [f"CODE_{code}" for code in unique_codes]

    def normalize_code_terms(self, text: str) -> List[str]:
        """提取文本中的代码术语并标准化为CODE_数字格式"""
        code_terms = self.CODE_PATTERN.findall(text)
        return [
            f"CODE_{match.group(1)}"
            for term in code_terms
            if term and (match := self._CODE_VALUE_REGEX.search(term))
        ]

    def custom_tokenize(self, text: str) -> tuple:
        """自定义分词函数"""
        try:
            import jieba
            # 使用jieba进行基础分词并过滤停用词
            filtered_tokens = [token.strip() for token in jieba.lcut(text)
                             if token.strip() and token.strip() not in self.STOPWORDS]
        except ImportError:
            # 简单分词作为备选
            tokens = text.split()
            filtered_tokens = [token for token in tokens if token not in self.STOPWORDS]

        # 提取并处理所有代码
        prefixed_codes = self.normalize_code_terms(text)
        standalone_codes = self.extract_and_process_codes(text)

        # 合并代码tokens
        code_tokens = list(set(prefixed_codes) | set(standalone_codes))

        return filtered_tokens, code_tokens

    def generate_number_variants(self, letter_digit_code: str) -> List[str]:
        """为字母+数字代码生成多种数字变体"""
        digit_match = re.search(r'\d+', letter_digit_code)
        if not digit_match:
            return [letter_digit_code]

        digit_str = digit_match.group()
        digit_num = int(digit_str)
        variants = [digit_str]

        # 对少于3位的数字补零
        if len(digit_str) < 3:
            if len(digit_str) == 1:
                variants.append(f"{digit_num:02d}")
            variants.append(f"{digit_num:03d}")

        # 去重并保持顺序
        return list(dict.fromkeys(variants))

    def replace_codes_in_text(self, text: str, codes: List[str]) -> List[str]:
        """根据已提取的codes进行替换"""
        # 创建替换映射
        replacement_map = {}
        for code in codes:
            if code.startswith("CODE_"):
                original_code = code[5:]  # 去掉"CODE_"前缀
                if re.match(r'^[Ff]\d+$', original_code):  # 只对以F或f开头的数字代码进行替换
                    replacement_map[original_code] = self.generate_number_variants(original_code)

        if not replacement_map:
            return []

        # 按key长度降序排序，避免短代码被长代码的子串替换
        sorted_codes = sorted(replacement_map.keys(), key=len, reverse=True)

        # 生成所有可能的替换组合
        result_texts = [text]

        for code in sorted_codes:
            new_texts = []
            for current_text in result_texts:
                for variant in replacement_map[code]:
                    new_text = current_text.replace(code, variant)
                    new_texts.append(new_text)
            result_texts = new_texts

        return result_texts

    def rewrite_query(self, query: str) -> Dict[str, Any]:
        """问题改写主接口"""
        try:
            # 提取代码
            segments, codes = self.custom_tokenize(query)

            # 生成文本变体
            variants = self.replace_codes_in_text(query, codes)

            result = {
                "original_query": query,
                "extracted_codes": codes,
                "tokenized_segments": segments,
                "query_variants": variants,
                "variant_count": len(variants)
            }

            return result

        except Exception as e:
            return {
                "original_query": query,
                "extracted_codes": [],
                "tokenized_segments": [],
                "query_variants": [],
                "variant_count": 0,
                "error": str(e)
            }

    def batch_rewrite_queries(self, queries: List[str]) -> List[Dict[str, Any]]:
        """批量处理查询改写"""
        results = []
        for query in queries:
            result = self.rewrite_query(query)
            results.append(result)
        return results


def demo_basic_functionality():
    """演示基本功能"""
    print("=" * 60)
    print("问题改写服务基本功能演示")
    print("=" * 60)
    
    service = SimpleQueryRewriteService()
    
    # 测试用例
    test_queries = [
        "SEW F43",
        "MC07B F111",
        "错误代码F7是什么？",
        "EMS小车控制器报警代码F008是什么？",
        "故障代码F111和F008的区别",
        "报警代码F43和F008的解决方案",
        "MC07B型号的错误代码F111",
        "MOVIFIT F08",
        "压铆设备C3卡钉",
        "SPR FAULT 655",
        "Movifit F08",
        "FDS 431",
        "FE020R1机器人曾经八角管坏过吗？什么原因坏的？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n测试 {i}: {query}")
        print("-" * 40)
        
        result = service.rewrite_query(query)
        
        print(f"原始查询: {result['original_query']}")
        print(f"提取的代码: {result['extracted_codes']}")
        print(f"分词结果: {result['tokenized_segments']}")
        print(f"变体数量: {result['variant_count']}")
        
        if result['query_variants']:
            print("查询变体:")
            for j, variant in enumerate(result['query_variants'], 1):
                print(f"  变体{j}: {variant}")
        else:
            print("无查询变体生成")


def demo_code_extraction():
    """演示代码提取功能"""
    print("\n" + "=" * 60)
    print("代码提取功能演示")
    print("=" * 60)
    
    service = SimpleQueryRewriteService()
    
    test_texts = [
        "错误代码123",
        "故障代码 F43",
        "报警代码F008",
        "SEW F43 设备故障",
        "MC07B F111 控制器",
        "ABC-123 型号设备",
        "设备ID为XYZ_789",
        "普通文本没有代码"
    ]
    
    for text in test_texts:
        print(f"\n文本: {text}")
        
        # 提取独立代码
        standalone_codes = service.extract_and_process_codes(text)
        print(f"独立代码: {standalone_codes}")
        
        # 提取带前缀代码
        prefixed_codes = service.normalize_code_terms(text)
        print(f"带前缀代码: {prefixed_codes}")
        
        # 完整分词
        segments, codes = service.custom_tokenize(text)
        print(f"分词结果: {segments}")
        print(f"所有代码: {codes}")


def demo_number_variants():
    """演示数字变体生成功能"""
    print("\n" + "=" * 60)
    print("数字变体生成功能演示")
    print("=" * 60)
    
    service = SimpleQueryRewriteService()
    
    test_codes = [
        "F7",      # 单位数
        "F43",     # 双位数
        "F111",    # 三位数
        "F008",    # 带前导零
        "ABC123",  # 字母+数字
        "XYZ"      # 纯字母
    ]
    
    for code in test_codes:
        variants = service.generate_number_variants(code)
        print(f"{code} -> {variants}")


def demo_batch_processing():
    """演示批量处理功能"""
    print("\n" + "=" * 60)
    print("批量处理功能演示")
    print("=" * 60)
    
    service = SimpleQueryRewriteService()
    
    queries = [
        "SEW F43 报警",
        "错误代码F7",
        "故障F111解决方案",
        "普通问题",
        "MC07B F008 控制器"
    ]
    
    print(f"批量处理 {len(queries)} 个查询:")
    for i, query in enumerate(queries, 1):
        print(f"  {i}. {query}")
    
    results = service.batch_rewrite_queries(queries)
    
    print(f"\n处理结果:")
    for i, result in enumerate(results, 1):
        print(f"\n查询 {i}: {result['original_query']}")
        print(f"  变体数量: {result['variant_count']}")
        if result['query_variants']:
            for j, variant in enumerate(result['query_variants'], 1):
                print(f"    变体{j}: {variant}")


def demo_real_world_examples():
    """演示真实世界的使用案例"""
    print("\n" + "=" * 60)
    print("真实世界使用案例演示")
    print("=" * 60)
    
    service = SimpleQueryRewriteService()
    
    # 模拟真实的用户查询
    real_queries = [
        "FDS常见故障处理手册中的错误代码414是什么意思？",
        "SEW F43 供料装置分离器卡住怎么办？",
        "MC07B控制器报F111故障如何解决？",
        "EMS小车控制器报警代码F008的处理方法",
        "压铆设备出现F7报警代码的原因分析",
        "故障代码F008和F43有什么区别？"
    ]
    
    for i, query in enumerate(real_queries, 1):
        print(f"\n案例 {i}:")
        print(f"用户查询: {query}")
        
        result = service.rewrite_query(query)
        
        print(f"提取的代码: {result['extracted_codes']}")
        print(f"生成的查询变体 ({result['variant_count']} 个):")
        
        if result['query_variants']:
            for j, variant in enumerate(result['query_variants'], 1):
                print(f"  {j}. {variant}")
        else:
            print("  (无变体生成)")
        
        print("-" * 50)


if __name__ == "__main__":
    print("QueryRewriteService 功能演示")
    print("=" * 60)
    
    # 运行所有演示
    demo_basic_functionality()
    demo_code_extraction()
    demo_number_variants()
    demo_batch_processing()
    demo_real_world_examples()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
