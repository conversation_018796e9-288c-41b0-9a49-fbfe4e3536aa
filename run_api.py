#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务启动脚本
"""

import uvicorn
import os
import argparse
from loguru import logger
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
if os.path.exists(env_file):
    load_dotenv(env_file)
    logger.info(f"已加载环境配置: {env_file}")
else:
    # 如果特定环境的配置文件不存在，尝试加载默认的.env文件
    if os.path.exists(".env"):
        load_dotenv(".env")
        logger.info("已加载默认环境配置: .env")
    else:
        logger.warning(f"未找到环境配置文件: {env_file} 或 .env")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="启动API服务")
    parser.add_argument(
        "--host", 
        type=str, 
        default="0.0.0.0", 
        help="服务主机地址"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8080, 
        help="服务端口"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="是否启用热重载"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="工作进程数量"
    )
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="info", 
        choices=["debug", "info", "warning", "error", "critical"],
        help="日志级别"
    )
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 打印启动信息
    logger.info(f"启动API服务: host={args.host}, port={args.port}, workers={args.workers}")
    logger.info(f"环境: {env_type}")
    
    # 启动服务
    uvicorn.run(
        "api.app:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level=args.log_level
    )

if __name__ == "__main__":
    main()