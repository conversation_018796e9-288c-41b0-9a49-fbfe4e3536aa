#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含格式化、计时等通用功能
"""

import time
import uuid
from datetime import datetime
from typing import Optional, List


class TimingManager:
    """计时管理器"""
    
    def __init__(self):
        """初始化计时管理器"""
        self.start_time = None
        self.reference_start_time = None
        self.reasoning_start_time = None
        self.content_start_time = None
        self.first_token_time = None
        self.first_token_received = False
        self.reasoning_started = False
        self.content_started = False
    
    def start_timing(self):
        """开始总计时"""
        self.start_time = time.time()
    
    def start_reference_timing(self):
        """开始参考内容计时"""
        if self.reference_start_time is None:
            self.reference_start_time = time.time()
            # 如果这是第一个响应，记录首token时间
            if self.first_token_time is None and self.start_time is not None:
                self.first_token_time = time.time() - self.start_time
    
    def start_reasoning_timing(self):
        """开始思考过程计时"""
        if self.reasoning_start_time is None:
            self.reasoning_start_time = time.time()
            self.reasoning_started = True
            # 如果这是第一个响应，记录首token时间
            if self.first_token_time is None and self.start_time is not None:
                self.first_token_time = time.time() - self.start_time
    
    def start_content_timing(self):
        """开始回复内容计时"""
        if self.content_start_time is None:
            self.content_start_time = time.time()
            self.content_started = True
            # 如果这是第一个响应，记录首token时间
            if self.first_token_time is None and self.start_time is not None:
                self.first_token_time = time.time() - self.start_time
    
    def get_total_time(self) -> float:
        """获取总耗时"""
        if self.start_time:
            return time.time() - self.start_time
        return 0
    
    def get_reference_time(self) -> float:
        """获取参考内容耗时"""
        if self.reference_start_time:
            return time.time() - self.reference_start_time
        return 0
    
    def get_reasoning_time(self) -> float:
        """获取思考过程耗时"""
        if self.reasoning_start_time:
            return time.time() - self.reasoning_start_time
        return 0
    
    def get_content_time(self) -> float:
        """获取回复内容耗时"""
        if self.content_start_time:
            return time.time() - self.content_start_time
        return 0
    
    def mark_first_token(self):
        """标记首token接收时间"""
        if self.first_token_time is None and self.start_time is not None:
            self.first_token_time = time.time() - self.start_time
            self.first_token_received = True
    
    def get_first_token_time(self) -> float:
        """获取首token响应时间"""
        return self.first_token_time if self.first_token_time is not None else 0
    
    def reset(self):
        """重置所有计时器"""
        self.start_time = None
        self.reference_start_time = None
        self.reasoning_start_time = None
        self.content_start_time = None
        self.first_token_time = None
        self.first_token_received = False
        self.reasoning_started = False
        self.content_started = False


class RequestHelper:
    """请求辅助工具"""
    
    @staticmethod
    def generate_request_id() -> str:
        """生成请求ID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_conversation_id() -> str:
        """生成对话ID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def create_payload(query: str, user_id: str, model_id: str,
                      history: list, stream: bool = True,
                      top_k: Optional[int] = None,
                      top_r: Optional[int] = None,
                      min_score: Optional[float] = None,
                      mode: Optional[str] = None,
                      temperature: Optional[float] = None,
                      top_p: Optional[float] = None,
                      knowledge: Optional[str] = None,
                      enable_thinking: Optional[bool] = None,
                      enable_web_search: Optional[bool] = None,
                      use_reranker: Optional[bool] = None,
                      collection: Optional[List[str]] = None) -> dict:
        """创建API请求负载

        Args:
            query: 用户查询
            user_id: 用户ID
            model_id: 模型ID
            history: 对话历史
            stream: 是否流式输出
            top_k: 检索数量
            top_r: 重排数量
            min_score: 最小相似度
            mode: 模式（前端：严谨/通用，API：strict/common）
            temperature: 温度参数
            top_p: Top-p参数
            knowledge: 知识库类型
            enable_thinking: 是否开启深度思考
            enable_web_search: 是否开启联网搜索
            use_reranker: 是否使用重排器
            collection: 集合选择列表（仅用于全库问答）

        Returns:
            请求负载字典
        """
        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": RequestHelper.generate_request_id(),
            "conversation_id": RequestHelper.generate_conversation_id(),
            "history": history[::-1],  # 反转历史记录
            "stream": stream
        }

        # 检索相关参数
        if top_k is not None:
            payload["top_k"] = top_k
        if top_r is not None:
            payload["top_r"] = top_r
        if min_score is not None:
            payload["min_score"] = min_score
        
        # 问答模式映射：前端"严谨"/"通用" -> API"strict"/"common"
        if mode is not None:
            if mode == "严谨":
                payload["mode"] = "strict"
            elif mode == "通用":
                payload["mode"] = "common"
            else:
                payload["mode"] = mode  # 保持原值

        # 模型相关参数
        if temperature is not None:
            payload["temperature"] = temperature
        if top_p is not None:
            payload["top_p"] = top_p

        # 知识库类型
        if knowledge is not None:
            payload["knowledge"] = knowledge

        # 深度思考开关 - 确保正确传递
        if enable_thinking is not None:
            payload["enable_thinking"] = bool(enable_thinking)

        # 联网搜索开关
        if enable_web_search is not None:
            payload["enable_web_search"] = bool(enable_web_search)

        # 重排器开关
        if use_reranker is not None:
            payload["use_reranker"] = bool(use_reranker)

        # 集合选择参数（仅用于全库问答）
        if collection is not None:
            payload["collection"] = collection

        return payload


class HistoryFormatter:
    """历史记录格式化工具"""
    
    @staticmethod
    def format_history_entry(query: str, content: str, 
                           total_time: float, 
                           reference_time: Optional[float] = None,
                           reasoning_time: Optional[float] = None,
                           content_time: Optional[float] = None,
                           first_token_time: Optional[float] = None) -> str:
        """格式化历史记录条目
        
        Args:
            query: 用户查询
            content: 助手回复
            total_time: 总耗时
            reference_time: 参考内容耗时
            reasoning_time: 思考过程耗时
            content_time: 回复内容耗时
            first_token_time: 首token响应时间
            
        Returns:
            格式化的历史记录条目
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 构建时间信息 - 确保所有参数都是数字类型
        if isinstance(total_time, (int, float)):
            time_info = f"总耗时: {total_time:.2f}秒"
        else:
            time_info = f"总耗时: {str(total_time)}"
        
        if first_token_time is not None and isinstance(first_token_time, (int, float)):
            time_info += f" | 首token: {first_token_time:.2f}秒"
        
        if reference_time is not None and isinstance(reference_time, (int, float)):
            time_info += f" | 检索: {reference_time:.2f}秒"
        
        if reasoning_time is not None and isinstance(reasoning_time, (int, float)):
            time_info += f" | 思考: {reasoning_time:.2f}秒"
        
        if content_time is not None and isinstance(content_time, (int, float)):
            time_info += f" | 回复: {content_time:.2f}秒"
        
        return (f"**[{timestamp}] 用户：**\n{query}\n\n"
                f"**[{timestamp}] 助手：**\n{content}\n\n"
                f"*{time_info}*\n\n---")
    
    @staticmethod
    def append_to_history(current_history: str, new_entry: str) -> str:
        """将新条目添加到历史记录
        
        Args:
            current_history: 当前历史记录
            new_entry: 新的历史条目
            
        Returns:
            更新后的历史记录
        """
        if current_history:
            return f"{current_history}\n\n{new_entry}"
        else:
            return new_entry


class ValidationHelper:
    """验证辅助工具"""
    
    @staticmethod
    def is_valid_query(query: str) -> bool:
        """验证查询是否有效
        
        Args:
            query: 用户查询
            
        Returns:
            是否有效
        """
        return bool(query and query.strip())
    
    @staticmethod
    def sanitize_query(query: str) -> str:
        """清理查询内容
        
        Args:
            query: 原始查询
            
        Returns:
            清理后的查询
        """
        if not query:
            return ""
        return query.strip()


class LogHelper:
    """日志辅助工具"""
    
    @staticmethod
    def format_log_message(api_type: str, action: str, request_id: str, 
                          user_id: str, model_id: str, query: str, 
                          **kwargs) -> str:
        """格式化日志消息
        
        Args:
            api_type: API类型
            action: 操作类型
            request_id: 请求ID
            user_id: 用户ID
            model_id: 模型ID
            query: 用户查询
            **kwargs: 其他参数
            
        Returns:
            格式化的日志消息
        """
        base_msg = f"[{api_type.upper()}][{action}] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}"
        
        if kwargs:
            extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            base_msg += f", {extra_info}"
        
        return base_msg