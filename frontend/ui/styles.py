#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI样式模块
包含所有CSS样式和JavaScript代码
"""

# 自定义CSS样式 - 全屏自适应设计
CUSTOM_CSS = """
/* 全屏自适应 */
html, body {
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: auto !important;
}

.gradio-container {
    min-height: 100vh !important;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding: 6px !important;
    margin: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
}

/* 主容器布局 - 真正的全屏 */
.main-container {
    min-height: calc(100vh - 16px) !important;
    display: flex !important;
    gap: 12px !important;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box !important;
}

/* 左侧配置区域 - 非常窄的紧凑布局 */
.config-sidebar {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
    background: #f5f5f5 !important;
    border-radius: 6px !important;
    padding: 2px !important;
    border: 1px solid #e0e0e0 !important;
    overflow-y: auto !important;
    height: calc(100vh - 16px) !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 2px !important;
    transition: width 0.3s ease, min-width 0.3s ease, max-width 0.3s ease !important;
}

/* 折叠状态的配置栏 */
.config-sidebar.collapsed {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    padding: 6px 2px !important;
}

/* 折叠状态下隐藏内容 */
.config-sidebar.collapsed > *:not(.sidebar-header) {
    display: none !important;
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin-bottom: 4px !important;
}

/* 折叠按钮 */
.collapse-btn {
    width: 30px !important;
    height: 30px !important;
    min-width: 30px !important;
    padding: 0 !important;
    font-size: 14px !important;
    font-weight: bold !important;
    border-radius: 4px !important;
    background: #007bff !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 100 !important;
}

.collapse-btn:hover {
    background: #0056b3 !important;
    transform: scale(1.05) !important;
}

/* 标题容器 - 加粗放大居中 */
.title-container {
    flex: 1 !important;
    font-size: 18px !important;
    font-weight: 800 !important;
    color: #2c3e50 !important;
    text-align: center !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 4px 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1) !important;
}

.title-container h1 {
    margin: 0 !important;
    padding: 0 !important;
    font-size: 18px !important;
    font-weight: 800 !important;
    text-align: center !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* 配置区域标题 - 更紧凑 */
.config-section {
    font-size: 12px !important;
    font-weight: bold !important;
    color: #495057 !important;
    margin: 0px 0 0px 0 !important;
    padding-bottom: 1px !important;
}
.config-section:first-child {
    margin-top: 0 !important;
}

/* 紧凑输入框样式 */
.compact-input {
    font-size: 12px !important;
    margin-bottom: 1px !important;
    margin-top: 0px !important;
    inline-size: 100% !important;
}

.compact-input input {
    padding: 0px 0px !important;
    font-size: 12px !important;
    margin-top: 0px !important;
    margin-bottom: 1px !important;
}

.compact-input label {
    display: inline-block !important;
    font-size: 12px !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
}

/* 固定宽度输入框样式 */
.fixed-width-input {
    width: 100% !important;
    max-width: 100px !important;
    min-width: 100px !important;
}

.fixed-width-input input,
.fixed-width-input select {
    display: inline-block !important;
    width: 100% !important;
    max-width: 100px !important;
    min-width: 100px !important;
}

/* 紧凑下拉框样式 */
.compact-dropdown {
    font-size: 12px !important;
    margin-bottom: 1px !important;
    margin-top: 0 !important;
}

.compact-dropdown select {
    padding: 1px 3px !important;
    font-size: 12px !important;
    height: 28px !important;
}

.compact-dropdown label {
    font-size: 12px !important;
    margin-bottom: 1px !important;
}

/* 紧凑滑块样式 */
.compact-slider {
    font-size: 12px !important;
    margin-bottom: 0px !important;
    margin-top: 0 !important;
}

.compact-slider label {
    font-size: 12px !important;
    margin-bottom: 0px !important;
}

/* 紧凑滑块样式 */
.search-compact-slider {
    font-size: 12px !important;
    margin-bottom: 0px !important;
    margin-top: 0 !important;
}

.search-compact-slider label {
    font-size: 12px !important;
    margin-bottom: 0px !important;
}

/* 紧凑复选框样式 - 真正的勾选框形式 */
.compact-checkbox {
    font-size: 14px !important;
    margin-bottom: 1px !important;
    margin-top: 1px !important;
    position: relative !important;
}

/* 显示原生勾选框 */
.compact-checkbox input[type="checkbox"] {
    display: inline-block !important;
    width: 18px !important;
    height: 18px !important;
    margin-right: 8px !important;
    cursor: pointer !important;
    accent-color: #4caf50 !important;
    transform: scale(1.2) !important;
}

.compact-checkbox label {
    font-size: 13px !important;
    font-weight: 600 !important;
    margin-bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
    padding: 8px 12px !important;
    background: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
    min-height: 36px !important;
}

.compact-checkbox label:hover {
    background: #e3f2fd !important;
    border-color: #2196f3 !important;
}

/* 选中状态样式 */
.compact-checkbox input[type="checkbox"]:checked + label {
    background: #e8f5e8 !important;
    border-color: #4caf50 !important;
    color: #2e7d32 !important;
}

/* 深度思考图标 */
.compact-checkbox label::before {
    content: "" !important;
    margin-right: 6px !important;
    font-size: 16px !important;
}

/* 确保勾选框可点击 */
.compact-checkbox,
.compact-checkbox label,
.compact-checkbox input {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* 覆盖Gradio默认样式 */
.compact-checkbox .gr-checkbox {
    background: none !important;
    border: none !important;
    padding: 0 !important;
}

/* 首token响应时间闪动效果 */
.first-token-display {
    transition: all 0.3s ease !important;
}

@keyframes blink {
    0%, 50% { 
        background-color: #fff3cd !important;
        border-color: #ffc107 !important;
    }
    51%, 100% { 
        background-color: #ffffff !important;
        border-color: #ced4da !important;
    }
}

/* 首token响应时间输入框样式 */
#first-token-time-display input {
    font-weight: bold !important;
    text-align: center !important;
    color: #495057 !important;
}

/* 紧凑单选框样式 */
.compact-radio {
    font-size: 12px !important;
    margin-bottom: 1px !important;
    margin-top: 1 !important;
}

.compact-radio label {
    font-size: 12px !important;
    margin-bottom: 0px !important;
}

.compact-radio .gr-radio-group {
    display: flex !important;
    flex-direction: row !important;
    gap: 15px !important;
    flex-wrap: nowrap !important;
    justify-content: flex-start !important;
    align-items: center !important;
}

.compact-radio input[type="radio"] {
    margin-right: 5px !important;
}

/* 确保单选框选项在同一行显示 */
.compact-radio .gr-radio-group > label {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    display: flex !important;
    align-items: center !important;
    margin-right: 0 !important;
}

/* 内联单选按钮样式 */
.inline-radio .gr-radio-group {
    display: flex !important;
    flex-direction: row !important;
    gap: 5px !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
}

.inline-radio .gr-radio-group > label {
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    margin-right: 0 !important;
}

/* 清空历史按钮样式 */
.clear-history-btn {
    font-size: 12px !important;
    padding: 4px 8px !important;
    margin-top: 5px !important;
    background: #dc3545 !important;
    color: white !important;
    border: none !important;
    border-radius: 2px !important;
    cursor: pointer !important;
    transition: background 0.3s ease !important;
}

.clear-history-btn:hover {
    background: #c82333 !important;
}

/* 控件间距更紧凑 */
.config-sidebar .gr-box, .config-sidebar .gr-form, .config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider, .config-sidebar .gr-button, .config-sidebar .gr-checkbox, .config-sidebar .gr-radio {
    margin-bottom: 1px !important;
    margin-top: 0 !important;
}
.config-sidebar .gr-button { margin-bottom: 1px !important; }
.config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider, .config-sidebar .gr-checkbox, .config-sidebar .gr-radio {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
.config-sidebar .gr-markdown {
    margin-bottom: 0px !important;
    margin-top: 0px !important;
    padding: 0 !important;
}

/* 配置栏内容区域更紧凑 */
#config-content-column {
    gap: 2px !important;
}

#config-content-column > * {
    margin-bottom: 1px !important;
    margin-top: 0 !important;
}

/* 右侧主要内容区域 - 充分利用剩余空间 */
.main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 16px) !important;
    width: calc(100vw - 204px) !important;
    max-width: calc(100vw - 204px) !important;
    overflow: visible !important;
    transition: width 0.3s ease, max-width 0.3s ease !important;
}

/* 配置栏折叠时的主内容区域 */
.main-content.expanded {
    width: calc(100vw - 64px) !important;
    max-width: calc(100vw - 64px) !important;
}

/* 侧边栏标题样式 */
.sidebar-title {
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 0 !important;
    color: #2c3e50 !important;
    text-align: left !important;
    padding: 0 !important;
    border: none !important;
    line-height: 30px !important;
}

/* 按钮样式 */
.gr-button {
    font-size: 14px !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
}

/* 小按钮 */
.small-button {
    font-size: 16px !important;
    padding: 5px 10px !important;
    min-height: 32px !important;
}

/* 紧凑按钮 - 更小更美观 */
.compact-button {
    font-size: 16px !important;
    padding: 4px 8px !important;
    min-height: 28px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.compact-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

/* 检索配置标题样式 - 居中美化 */
.search-config-header {
    text-align: center !important;
    margin-bottom: 1px !important;
    margin-top: 2px !important;
}

.search-config-title {
    font-size: 16px !important;
    font-weight: bold !important;
    color: #495057 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
    padding: 2px 0 !important;
    border-bottom: 2px solid #e9ecef !important;
    margin-bottom: 2px !important;
}

/* 检索配置滑块样式 - 居中对齐 */
.search-config-slider {
    text-align: center !important;
    margin: 4px 0 !important;
}

.search-config-slider label {
    text-align: center !important;
    display: block !important;
    font-weight: 600 !important;
    color: #495057 !important;
}

/* 输入框样式 */
.gr-textbox {
    font-size: 14px !important;
}

/* Markdown渲染 */
.gr-markdown {
    font-size: 14px !important;
    line-height: 1.4 !important;
}

/* 紧凑标签样式 */
.compact-label {
    margin-bottom: 2px !important;
    margin-top: 4px !important;
    padding: 2px 4px !important;
    background: #f8f9fa !important;
    border-radius: 3px !important;
    border: 1px solid #e9ecef !important;
    font-size: 10px !important;
    font-weight: bold !important;
}

/* 标签页样式 */
.gr-tab-nav {
    font-size: 14px !important;
    margin-bottom: 8px !important;
}

/* 表单间距 */
.gr-form {
    gap: 2px !important;
}

/* 面板样式 */
.gr-panel {
    padding: 6px !important;
    border-radius: 4px !important;
}

/* 组件边界 - 紧凑布局 */
.component-border {
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    padding: 6px !important;
    margin-bottom: 1px !important;
    overflow-y: auto !important;
    resize: vertical !important;
    min-height: 150px !important;
    height: auto !important;
    position: relative !important;
}

/* 确保复制按钮不被遮挡 */
.component-border .gr-copy-button {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    z-index: 10 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border-radius: 4px !important;
}

/* 参考内容和思考过程的小字体 */
.reference-content .gr-markdown,
.reasoning-content .gr-markdown,
.data-reference-content .gr-markdown {
    font-size: 13px !important;
    line-height: 1.3 !important;
}

/* 主要响应区域布局 - 强制水平布局 */
.main-response-area {
    display: flex !important;
    flex-direction: row !important;
    gap: 12px !important;
    margin-bottom: 8px !important;
    width: 100% !important;
    flex-wrap: nowrap !important;
    align-items: flex-start !important;
    justify-content: space-between !important;
}

/* 左侧知识库参考列 - 强制固定宽度 */
.left-reference-column {
    flex: 0 0 48% !important;
    width: 48% !important;
    min-width: 48% !important;
    max-width: 48% !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: visible !important;
}

/* 右侧内容列 - 强制固定宽度 */
.right-content-column {
    flex: 0 0 52% !important;
    width: 52% !important;
    min-width: 52% !important;
    max-width: 5252% !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: visible !important;
}

/* 确保Gradio的Column组件不会覆盖我们的样式 */
.main-response-area > .gr-column {
    display: flex !important;
    flex-direction: column !important;
}

.left-reference-column.gr-column {
    flex: 0 0 48% !important;
    width: 48% !important;
    min-width: 48% !important;
    max-width: 48% !important;
}

.right-content-column.gr-column {
    flex: 0 0 52% !important;
    width: 52% !important;
    min-width: 52% !important;
    max-width: 52% !important;
}

/* 固定高度的组件 - 思考过程、知识库参考、对话历史、数据参考 */
.component-border.fixed-height.reasoning-content {
    max-height: 250px !important;
    height: 250px !important;
}
.component-border.dynamic-height.reference-content {
    max-height: 1240px !important;
    height: auto !important;
    min-height: 590px !important;
}
.component-border.dynamic-height.history-content {
    max-height: 800px !important;
    height: auto !important;
    min-height: 400px !important;
}
.component-border.dynamic-height.data-reference-content {
    max-height: 1240px !important;
    height: auto !important;
    min-height: 590px !important;
}

/* 动态高度的组件 - 回复内容 */
.component-border.dynamic-height {
    max-height: 920px !important;
    height: auto !important;
    min-height: 265px !important;
}

/* 折叠组件样式 - 统一样式 */
.collapsible-reasoning,
.collapsible-reference,
.collapsible-content,
.collapsible-history,
.collapsible-data-reference {
    margin-bottom: 8px !important;
}

.collapsible-reasoning .gr-accordion-header,
.collapsible-reference .gr-accordion-header,
.collapsible-content .gr-accordion-header,
.collapsible-history .gr-accordion-header,
.collapsible-data-reference .gr-accordion-header {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

.collapsible-reasoning .gr-accordion-content,
.collapsible-reference .gr-accordion-content,
.collapsible-content .gr-accordion-content,
.collapsible-history .gr-accordion-content,
.collapsible-data-reference .gr-accordion-content {
    border: none !important;
    padding: 0 !important;
    margin-top: 4px !important;
}

/* 标签和耗时显示 - 紧贴内容框 */
.label-with-timing {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 2px !important;
    margin-top: 8px !important;
    padding: 4px 8px !important;
    background: #f8f9fa !important;
    border-radius: 4px !important;
    border: 1px solid #e9ecef !important;
}

.timing-display {
    font-size: 11px !important;
    color: #6c757d !important;
    font-weight: normal !important;
    background: #fff !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    border: 1px solid #dee2e6 !important;
}

/* 知识库参考和数据参考内容字体 */
.reference-content {
    font-size: 13px !important;
    line-height: 1.4 !important;
}

/* 思考过程内容字体 */
.reasoning-content {
    font-size: 13px !important;
    line-height: 1.4 !important;
}

/* Gradio内置复制按钮样式修复 - 确保不被圆角边框遮挡 */
.gr-markdown .copy-button,
.gr-textbox .copy-button,
[data-testid="markdown"] .copy-button {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: #007bff !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    cursor: pointer !important;
    z-index: 1002 !important;
    opacity: 0.9 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    margin: 0 !important;
}

.gr-markdown .copy-button:hover,
.gr-textbox .copy-button:hover,
[data-testid="markdown"] .copy-button:hover {
    opacity: 1 !important;
    background: #0056b3 !important;
    transform: scale(1.05) !important;
}

/* 确保Markdown容器有足够的内边距给复制按钮 */
.gr-markdown,
[data-testid="markdown"] {
    position: relative !important;
    padding-right: 70px !important;
    padding-top: 35px !important;
}

/* 修复圆角边框对复制按钮的遮挡 */
.component-border .gr-markdown,
.component-border [data-testid="markdown"] {
    border-radius: 6px !important;
    overflow: visible !important;
}

/* Markdown组件样式 */
.gr-markdown {
    max-height: none !important;
    overflow-y: auto !important;
}

/* 标签页内容区域 */
.tab-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: visible !important;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .config-sidebar {
        width: 160px !important;
        min-width: 160px !important;
        max-width: 160px !important;
    }

    .main-content {
        width: calc(100vw - 184px) !important;
        max-width: calc(100vw - 184px) !important;
    }

    .main-content.expanded {
        width: calc(100vw - 64px) !important;
        max-width: calc(100vw - 64px) !important;
    }

    /* 确保在中等屏幕上保持水平布局 */
    .main-response-area {
        flex-direction: row !important;
        flex-wrap: nowrap !important;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column !important;
    }
    .config-sidebar {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
    }
    
    /* 在小屏幕上改为垂直布局 */
    .main-response-area {
        flex-direction: column !important;
    }
    
    .left-reference-column,
    .right-content-column {
        flex: 1 1 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 8px !important;
    }
}


/* 隐藏未使用的标签变量 */
.hidden-label {
    display: none !important;
}

/* 动态配置区域样式 */
#dynamic-config-area {
    margin-top: 4px !important;
}

#llm-config,
#rag-config,
#search-config {
    transition: opacity 0.3s ease-in-out !important;
    padding-top: 1 !important;
    margin-top: 1 !important;
}

/* 配置组样式 */
.gr-group {
    border: 1px solid #e9ecef !important;
    border-radius: 6px !important;
    padding: 4px !important;
    margin-bottom: 4px !important;
    background: #ffffff !important;
}

/* 确保隐藏的配置组不占用空间 */
.gr-group[style*="display: none"] {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
}

/* 折叠按钮增强样式 */
#collapse-btn {
    cursor: pointer !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: auto !important;
}

#collapse-btn:active {
    transform: scale(0.95) !important;
}
"""