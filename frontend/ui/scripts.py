#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript脚本模块
包含前端交互逻辑
"""

# JavaScript代码
CUSTOM_JAVASCRIPT = """
<script>
// 全局计时器管理 - 专注于首token响应时间
window.timingManager = {
    firstTokenStartTime: null,
    blinkInterval: null,

    // 更新首token响应时间显示
    updateFirstTokenTime: function(seconds) {
        // 通过ID查找首token响应时间输入框
        const element = document.getElementById('first-token-time-display');
        if (element) {
            const input = element.querySelector('input');
            if (input) {
                input.value = seconds.toFixed(1) + '秒';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }

        // 备用方法：通过标签查找
        const textboxes = document.querySelectorAll('.gr-textbox input');
        textboxes.forEach(input => {
            const label = input.closest('.gr-textbox').querySelector('label');
            if (label && label.textContent.includes('首token响应时间')) {
                input.value = seconds.toFixed(1) + '秒';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
        });
    },

    // 开始闪动效果
    startBlinking: function() {
        const element = document.getElementById('first-token-time-display');
        if (element) {
            element.style.animation = 'blink 1s infinite';
        }
    },

    // 停止闪动效果
    stopBlinking: function() {
        const element = document.getElementById('first-token-time-display');
        if (element) {
            element.style.animation = 'none';
        }
    },

    // 开始首token计时
    startFirstTokenTimer: function() {
        this.firstTokenStartTime = Date.now();
        this.updateFirstTokenTime(0);
        this.startBlinking();
    },

    // 结束首token计时
    endFirstTokenTimer: function() {
        if (this.firstTokenStartTime) {
            const elapsed = (Date.now() - this.firstTokenStartTime) / 1000;
            this.updateFirstTokenTime(elapsed);
            this.stopBlinking();
            this.firstTokenStartTime = null;
            return elapsed;
        }
        return 0;
    }
};

// 配置栏折叠管理 - 修复版本
window.sidebarManager = {
    isCollapsed: false,

    // 切换折叠状态
    toggleCollapse: function() {
        console.log('toggleCollapse called');
        const sidebar = document.querySelector('#config-sidebar-container, .config-sidebar');
        const mainContent = document.querySelector('.main-content');
        const collapseBtn = document.getElementById('collapse-btn');

        console.log('Elements found:', {
            sidebar: !!sidebar,
            mainContent: !!mainContent,
            collapseBtn: !!collapseBtn
        });

        if (sidebar && collapseBtn) {
            this.isCollapsed = !this.isCollapsed;
            console.log('Toggling to collapsed:', this.isCollapsed);

            if (this.isCollapsed) {
                // 折叠状态
                sidebar.style.width = '50px';
                sidebar.style.minWidth = '50px';
                sidebar.style.maxWidth = '50px';
                sidebar.classList.add('collapsed');
                collapseBtn.textContent = '▶';
                
                // 隐藏侧边栏内容（除了头部）
                const sidebarChildren = sidebar.children;
                for (let i = 0; i < sidebarChildren.length; i++) {
                    const child = sidebarChildren[i];
                    if (!child.classList.contains('sidebar-header')) {
                        child.style.display = 'none';
                    }
                }
                
                // 调整主内容区域
                if (mainContent) {
                    mainContent.classList.add('expanded');
                }
            } else {
                // 展开状态
                sidebar.style.width = '400px';
                sidebar.style.minWidth = '400px';
                sidebar.style.maxWidth = '400px';
                sidebar.classList.remove('collapsed');
                collapseBtn.textContent = '◀';
                
                // 显示侧边栏内容
                const sidebarChildren = sidebar.children;
                for (let i = 0; i < sidebarChildren.length; i++) {
                    const child = sidebarChildren[i];
                    if (!child.classList.contains('sidebar-header')) {
                        child.style.display = '';
                    }
                }
                
                // 调整主内容区域
                if (mainContent) {
                    mainContent.classList.remove('expanded');
                }
            }
            
            // 强制重新计算布局
            setTimeout(function() {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        } else {
            console.error('Could not find required elements for collapse');
        }
    }
};

// 初始化函数 - 支持reload加载
function initializeApp() {
    console.log('App initializing...');
    
    // 动态更新响应时间标签
    function updateResponseTimeLabel(mode) {
        const element = document.getElementById('first-token-time-display');
        if (element) {
            const label = element.querySelector('label');
            if (label) {
                if (mode === 'search') {
                    label.textContent = '检索时间';
                } else {
                    label.textContent = '首token响应时间';
                }
            }
        }
    }
    
    // 监听折叠按钮点击 - 使用事件委托
    document.addEventListener('click', function(e) {
        console.log('Click detected on:', e.target);
        
        // 检查是否点击了折叠按钮
        if (e.target.id === 'collapse-btn' || 
            e.target.closest('#collapse-btn') || 
            e.target.classList.contains('collapse-btn')) {
            console.log('Collapse button clicked');
            e.preventDefault();
            e.stopPropagation();
            window.sidebarManager.toggleCollapse();
            return;
        }

        // 监听标签页切换，更新响应时间标签
        if (e.target.closest('.gr-tab-nav')) {
            const tabText = e.target.textContent || '';
            setTimeout(() => {
                if (tabText.includes('检索')) {
                    updateResponseTimeLabel('search');
                } else {
                    updateResponseTimeLabel('token');
                }
            }, 100);
        }

        // 监听发送按钮点击
        if ((e.target.textContent === '发送' || e.target.textContent === '检索') &&
            (e.target.classList.contains('primary') || e.target.classList.contains('gr-button'))) {
            console.log('Send/Search button clicked');
            // 开始首token计时
            window.timingManager.startFirstTokenTimer();
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeApp);

// 支持reload加载方式
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // 如果DOM已经加载完成，直接初始化
    initializeApp();
}

// 兼容性初始化 - 确保在各种加载情况下都能正常工作
window.addEventListener('load', function() {
    // 延迟执行，确保Gradio完全加载
    setTimeout(initializeApp, 500);
});

// Gradio特定的初始化
if (typeof window.gradio !== 'undefined') {
    initializeApp();
} else {
    // 等待Gradio加载
    const checkGradio = setInterval(() => {
        if (typeof window.gradio !== 'undefined' || document.querySelector('.gradio-container')) {
            clearInterval(checkGradio);
            initializeApp();
        }
    }, 100);
}

// 原有的初始化逻辑保持不变
(function() {
    console.log('Legacy initialization...');
    
    // 定期检查并重新绑定折叠按钮（防止Gradio重新渲染后丢失事件）
    setInterval(function() {
        const collapseBtn = document.getElementById('collapse-btn');
        if (collapseBtn && !collapseBtn.hasAttribute('data-collapse-bound')) {
            console.log('Re-binding collapse button');
            collapseBtn.setAttribute('data-collapse-bound', 'true');
            collapseBtn.addEventListener('click', function(e) {
                console.log('Direct collapse button click');
                e.preventDefault();
                e.stopPropagation();
                window.sidebarManager.toggleCollapse();
            });
        }
        
        // 确保下拉框可交互
        const dropdowns = document.querySelectorAll('.compact-dropdown select');
        dropdowns.forEach(dropdown => {
            if (!dropdown.hasAttribute('data-interactive-bound')) {
                dropdown.setAttribute('data-interactive-bound', 'true');
                dropdown.disabled = false;
                dropdown.style.pointerEvents = 'auto';
            }
        });
        
        // 强制更新深度思考按钮状态显示
        const checkboxes = document.querySelectorAll('.compact-checkbox input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            const label = checkbox.nextElementSibling;
            if (label && label.tagName === 'LABEL') {
                if (checkbox.checked) {
                    label.style.background = 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)';
                    label.style.borderColor = '#4caf50';
                    label.style.color = 'white';
                    label.style.boxShadow = '0 4px 12px rgba(76, 175, 80, 0.3)';
                } else {
                    label.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
                    label.style.borderColor = '#dee2e6';
                    label.style.color = '#495057';
                    label.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                }
            }
        });
    }, 1000);
    
    // 确保布局正确应用
    function enforceLayout() {
        // 强制应用水平布局
        const responseAreas = document.querySelectorAll('.main-response-area');
        responseAreas.forEach(area => {
            area.style.display = 'flex';
            area.style.flexDirection = 'row';
            area.style.flexWrap = 'nowrap';
            area.style.width = '100%';
            area.style.gap = '8px';
        });
        
        // 强制左侧知识库参考列宽度
        const leftColumns = document.querySelectorAll('.left-reference-column, .left-reference-column.gr-column');
        leftColumns.forEach((col, index) => {
            console.log(`处理左侧列 ${index}:`, col);
            col.style.flex = '0 0 48%';
            col.style.width = '48%';
            col.style.minWidth = '48%';
            col.style.maxWidth = '48%';
            col.style.display = 'flex';
            col.style.flexDirection = 'column';
        });
        
        // 强制右侧内容列宽度
        const rightColumns = document.querySelectorAll('.right-content-column, .right-content-column.gr-column');
        rightColumns.forEach((col, index) => {
            console.log(`处理右侧列 ${index}:`, col);
            col.style.flex = '0 0 48%';
            col.style.width = '48%';
            col.style.minWidth = '48%';
            col.style.maxWidth = '48%';
            col.style.display = 'flex';
            col.style.flexDirection = 'column';
        });
    }
    
    // 初始应用布局
    setTimeout(enforceLayout, 100);
    
    // 监听标签页切换，重新应用布局
    document.addEventListener('click', function(e) {
        if (e.target.closest('.gr-tab-nav')) {
            setTimeout(enforceLayout, 100);
        }
    });

    // 监听内容变化，检测首token响应
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                // 检查是否有新内容出现（第一个chunk）
                const target = mutation.target;
                if (target.closest && target.closest('[data-testid="markdown"]')) {
                    // 检查是否有实际内容（不是空白）
                    const content = target.textContent || target.innerText || '';
                    if (content.trim().length > 0 && window.timingManager.firstTokenStartTime) {
                        // 结束首token计时并停止闪动
                        window.timingManager.endFirstTokenTimer();
                    }
                }
                
                // 检查首token时间显示是否包含✓标记，如果有则停止闪动
                if (target.nodeType === Node.TEXT_NODE || target.nodeType === Node.ELEMENT_NODE) {
                    const element = target.closest ? target.closest('#first-token-time-display') : null;
                    if (element) {
                        const input = element.querySelector('input');
                        if (input && input.value.includes('✓')) {
                            window.timingManager.stopBlinking();
                        }
                    }
                }
            }
        });
    });

    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
});
</script>
"""