# IPD Agent - 智能问答助手

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://www.python.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.95+-green.svg)](https://fastapi.tiangolo.com/)
[![Gradio](https://img.shields.io/badge/Gradio-4.0+-orange.svg)](https://gradio.app/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📖 项目简介

IDP Agent 是一个基于大语言模型的智能问答助手系统，支持多种问答模式和知识库检索。系统采用模块化设计，提供了完整的 RAG（检索增强生成）解决方案，支持多领域知识库问答。

### 🌟 核心特性

- **多模式问答支持**：LLM问答、RAG问答、数据问答、汽车知识库问答、全库问答、实时检索
- **流式响应**：支持实时流式输出，提升用户体验
- **多知识库检索**：支持硬件知识库、R平台数据、汽车知识库等多个专业领域
- **智能重排序**：基于语义相似度的文档重排序机制
- **Web界面**：基于 Gradio 的现代化 Web 界面
- **RESTful API**：完整的 API 接口，支持第三方集成
- **对话管理**：支持多轮对话和历史记录管理
- **性能监控**：完整的日志记录和性能监控

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   API Gateway   │    │   Core Engine   │
│   (Gradio)      │◄──►│   (FastAPI)     │◄──►│   (Pipelines)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Config Mgmt   │    │   Services      │    │   LLM Provider  │
│   (Configs)     │    │   (Search/      │    │   (OpenAI/      │
│                 │    │    Rerank)      │    │    Qwen)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Data Storage  │
                       │   (Redis/       │
                       │    Vector DB)   │
                       └─────────────────┘
```

## 📁 项目结构

```
idp_agent/
├── api/                   # API层
│   ├── app.py             # FastAPI应用入口
│   └── routes.py          # API路由定义
├── core/                  # 核心模块
│   ├── llm_provider.py    # LLM提供者
│   └── schemas.py         # 数据模型
├── pipelines/             # 处理管道
│   ├── llm_qa.py         # LLM问答
│   ├── rag_qa.py         # RAG问答
│   ├── data_qa.py        # 数据问答
│   ├── car_qa.py         # 汽车问答
│   ├── all_qa.py         # 全库问答
│   └── search.py         # 检索功能
├── services/              # 业务服务
│   ├── search_service.py  # 搜索服务
│   ├── rerank_service.py  # 重排服务
│   ├── chat_storage_service.py # 聊天存储
│   └── redis_service.py   # Redis服务
├── config/                # 配置文件
│   ├── model_config.py    # 模型配置
│   ├── search_config.py   # 搜索配置
│   └── redis_config.py    # Redis配置
├── frontend/              # 前端代码
│   ├── gradio_app_v2.py  # Gradio应用
│   ├── ui/               # UI组件
│   ├── handlers/         # 事件处理器
│   └── api/              # API客户端
├── prompts/               # 提示词模板
├── tests/                 # 测试代码
├── utils/                 # 工具函数
├── docs/                  # 文档
├── requirements.txt       # 依赖列表
├── run_api.py            # API启动脚本
├── run_gradio.py         # 前端启动脚本
├── run_local.sh          # 本地开发启动脚本
├── docker-compose.yml    # Docker编排文件
└── README.md             # 项目说明
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.9+ (推荐 3.10+)
- **Redis**: 6.0+ (用于缓存和会话管理)
- **内存**: 最少 4GB RAM (推荐 8GB+)
- **存储**: 至少 10GB 可用空间
- **网络**: 需要访问外部 LLM API 服务

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd idp_agent
```

#### 2. 创建虚拟环境（推荐）
```bash
# 使用 venv
python3 -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 或使用 conda
conda create -n idp-agent python=3.10
conda activate idp-agent
```

#### 3. 安装依赖
```bash
# 安装核心依赖
pip install -r requirements.txt

# 如果需要 Hera 环境支持
pip install -r requirements.hera.txt
```

#### 4. 配置环境变量
```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置文件，设置必要的环境变量
vim .env
```

**重要配置项说明：**
```bash
# API 访问令牌（必须设置）
API_ACCESS_TOKEN=your_secure_token_here

# 模型 API 密钥（至少配置一个）
OPENAI_API_KEY=your_openai_key
QWEN_API_KEY=your_qwen_key

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 搜索服务配置
SEARCH_SERVICE_URL=http://localhost:8001
RERANK_SERVICE_URL=http://localhost:8002
```

#### 5. 启动 Redis 服务
```bash
# 使用 Docker 启动 Redis
docker run -d --name redis -p 6379:6379 redis:6-alpine

# 或使用系统安装的 Redis
redis-server
```

#### 6. 启动服务

**方式一：本地开发启动（推荐）**
```bash
# 启动 API 服务（支持热重载）
./run_local.sh

# 在另一个终端启动前端界面
python run_gradio.py
```

**方式二：生产环境启动**
```bash
# 启动 API 服务
./run.sh

# 启动前端界面
python run_gradio.py
```

**方式三：手动启动**
```bash
# 启动 API 服务
python run_api.py --host 0.0.0.0 --port 8080 --workers 4

# 启动前端界面
python frontend/gradio_app_v2.py
```

#### 7. 访问服务
- **Web 界面**: http://localhost:7862
- **API 文档**: http://localhost:8080/docs
- **健康检查**: http://localhost:8080/api/v1/health

### 🐳 Docker 部署

#### 单容器部署
```bash
# 构建镜像
docker build -t idp-agent .

# 运行容器
docker run -d \
  -p 8080:8080 \
  -p 7862:7862 \
  -e API_ACCESS_TOKEN=your_token \
  -e OPENAI_API_KEY=your_key \
  --name idp-agent \
  idp-agent
```

#### Docker Compose 部署（推荐）
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，设置必要的配置

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f idp-agent
```

**Docker Compose 服务说明：**
- `idp-agent`: 主应用服务
- `redis`: 缓存服务
- `nginx`: 反向代理（生产环境）
- `prometheus`: 监控服务（可选）
- `grafana`: 监控面板（可选）

#### 生产环境部署
```bash
# 启动生产环境配置
docker-compose --profile production up -d

# 启动监控服务
docker-compose --profile monitoring up -d
```

## 📚 功能模块

### 1. LLM 问答 (💬)
- 基础的大语言模型问答
- 支持多轮对话
- 无需知识库检索

### 2. 硬工知识库 (📚)
- 基于硬件工程知识库的 RAG 问答
- 支持技术文档检索
- 专业领域问答

### 3. R平台问答 (🔍)
- 基于 R 平台数据的问答
- 数据驱动的回答
- 支持数据查询和分析

### 4. 汽车知识库 (🚗)
- 汽车领域专业知识问答
- 汽车技术文档检索
- 行业专业术语支持

### 5. 全库问答 (🌐)
- 跨多个知识库的综合问答
- 智能路由到最相关的知识库
- 全面的知识覆盖

### 6. 检索功能 (🔎)
- 纯检索功能，不生成回答
- 支持多库并行检索
- 结果重排序和过滤

## 🔧 配置说明

### 主要配置文件

- `config/model_config.py` - 模型配置
- `config/search_config.py` - 检索配置
- `config/redis_config.py` - Redis 配置
- `frontend/frontend_config.py` - 前端配置

### 环境变量详解

#### API 配置
```bash
API_BASE_URL=http://localhost:8080/api/v1  # API基础URL
API_ACCESS_TOKEN=your_token                # API访问令牌
API_PORT=8080                             # API服务端口
API_HOST=0.0.0.0                          # API服务主机
```

#### 模型配置
```bash
DEFAULT_MODEL=qwen3_32b                   # 默认使用的模型
OPENAI_API_KEY=your_openai_key           # OpenAI API密钥
OPENAI_BASE_URL=https://api.openai.com/v1 # OpenAI API地址
QWEN_API_KEY=your_qwen_key               # 通义千问API密钥
```

#### Redis 配置
```bash
REDIS_HOST=localhost                      # Redis主机地址
REDIS_PORT=6379                          # Redis端口
REDIS_PASSWORD=                          # Redis密码
REDIS_DB=0                               # Redis数据库编号
CONVERSATION_TIMEOUT=3600                # 会话超时时间（秒）
```

#### 检索配置
```bash
DEFAULT_TOP_K=20                         # 默认检索数量
DEFAULT_TOP_R=10                         # 默认重排数量
DEFAULT_MIN_SCORE=0.7                    # 最小相似度阈值
SEARCH_SERVICE_URL=http://localhost:8001  # 搜索服务地址
RERANK_SERVICE_URL=http://localhost:8002  # 重排服务地址
```

## 🔌 API 接口

### 主要端点

| 端点 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/api/v1/llm-qa` | POST | LLM 问答 | ✅ |
| `/api/v1/rag-qa` | POST | RAG 问答 | ✅ |
| `/api/v1/data-qa` | POST | 数据问答 | ✅ |
| `/api/v1/car-qa` | POST | 汽车知识库问答 | ✅ |
| `/api/v1/all-qa` | POST | 全库问答 | ✅ |
| `/api/v1/search` | POST | 检索接口 | ✅ |
| `/api/v1/health` | GET | 健康检查 | ❌ |

### 请求示例

#### LLM 问答
```bash
curl -X POST "http://localhost:8080/api/v1/llm-qa" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "什么是人工智能？",
    "user_id": "user123",
    "model_id": "qwen3_32b",
    "msg_id": "msg123",
    "conversation_id": "conv123",
    "history": [],
    "stream": true
  }'
```

#### RAG 问答
```bash
curl -X POST "http://localhost:8080/api/v1/rag-qa" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "硬件设计的基本原则是什么？",
    "user_id": "user123",
    "model_id": "qwen3_32b",
    "top_k": 10,
    "top_r": 5,
    "min_score": 0.7,
    "stream": false
  }'
```

#### 检索接口
```bash
curl -X POST "http://localhost:8080/api/v1/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "汽车发动机工作原理",
    "collections": ["car_collection"],
    "top_k": 20,
    "min_score": 0.6
  }'
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "answer": "这是回答内容...",
    "sources": [
      {
        "title": "文档标题",
        "content": "相关内容片段",
        "score": 0.85,
        "metadata": {}
      }
    ],
    "conversation_id": "conv123",
    "msg_id": "msg123"
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "error": "详细错误信息"
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定测试模块
pytest tests/test_api.py
pytest tests/test_pipelines.py

# 运行测试并生成覆盖率报告
pytest --cov=. tests/ --cov-report=html

# 运行测试并输出详细信息
pytest -v tests/

# 运行特定测试用例
pytest tests/test_api.py::test_llm_qa -v
```

### 测试环境配置
```bash
# 设置测试环境变量
export TEST_MODE=true
export ENVIRONMENT=test

# 使用测试配置文件
cp .env.example .env.test
```

### API 测试示例
```bash
# 健康检查
curl http://localhost:8080/api/v1/health

# LLM 问答测试
curl -X POST "http://localhost:8080/api/v1/llm-qa" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test_token" \
  -d '{"query": "测试问题", "user_id": "test_user"}'
```

## 📊 性能监控

### 监控指标

系统提供了完整的性能监控和日志记录：

- **响应时间监控**: API 请求响应时间统计
- **吞吐量监控**: QPS（每秒查询数）统计
- **错误率监控**: 错误请求比例统计
- **资源使用监控**: CPU、内存、磁盘使用情况
- **缓存命中率**: Redis 缓存效果监控

### 日志系统

- **API 日志**: `logs/api.log` - API 请求和响应日志
- **前端日志**: `logs/frontend.log` - Gradio 前端日志
- **错误日志**: `logs/error.log` - 系统错误日志
- **性能日志**: `logs/performance.log` - 性能指标日志

### 健康检查

```bash
# 检查 API 服务状态
curl http://localhost:8080/api/v1/health

# 检查 Redis 连接
redis-cli ping

# 检查服务进程
ps aux | grep "run_api\|gradio"
```

