version: '3.8'

services:
  # IDP Agent 主服务
  idp-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: idp-agent
    ports:
      - "8080:8080"  # API端口
      - "7862:7862"  # Gradio前端端口
    environment:
      # API配置
      - API_BASE_URL=http://localhost:8080/api/v1
      - API_ACCESS_TOKEN=${API_ACCESS_TOKEN}
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # 模型配置
      - DEFAULT_MODEL=${DEFAULT_MODEL:-qwen3_32b}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - QWEN_API_KEY=${QWEN_API_KEY}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # 性能配置
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-600}
      - MAX_WORKERS=${MAX_WORKERS:-4}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./data:/app/data
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - idp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 缓存服务
  redis:
    image: redis:6-alpine
    container_name: idp-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - idp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: idp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - idp-agent
    restart: unless-stopped
    networks:
      - idp-network
    profiles:
      - production

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: idp-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - idp-network
    profiles:
      - monitoring

  # Grafana 监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: idp-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - idp-network
    profiles:
      - monitoring

  # 日志收集 (可选)
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    container_name: idp-filebeat
    user: root
    volumes:
      - ./logs:/app/logs:ro
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - idp-agent
    restart: unless-stopped
    networks:
      - idp-network
    profiles:
      - logging

# 网络配置
networks:
  idp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
