#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符数长度限制工具类
用于限制输入到模型服务的字符数长度，防止超过模型限制
"""

import json
import logging
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)


class CharacterLimiter:
    """字符数长度限制器"""

    def __init__(self, max_chars: int = 120000):
        """
        初始化字符数限制器

        Args:
            max_chars: 最大字符数量，默认12万字
        """
        self.max_chars = max_chars
        self.logger = logger
    
    def count_chars(self, text: str) -> int:
        """
        计算文本的字符数量

        Args:
            text: 输入文本

        Returns:
            字符数量
        """
        if not text:
            return 0
        return len(text)
    
    def truncate_text(self, text: str, max_chars: int) -> str:
        """
        截断文本到指定字符长度

        Args:
            text: 输入文本
            max_chars: 最大字符数量

        Returns:
            截断后的文本
        """
        if not text:
            return text

        current_chars = len(text)
        if current_chars <= max_chars:
            return text

        # 直接截断到指定长度
        truncated = text[:max_chars]
        self.logger.info(f"文本截断: 原长度{current_chars}, 截断后长度{len(truncated)}")

        return truncated
    
    def limit_history(self, history: List[Dict], max_chars: int) -> List[Dict]:
        """
        限制历史消息的字符长度

        Args:
            history: 历史消息列表
            max_chars: 最大字符数量

        Returns:
            限制后的历史消息列表
        """
        if not history:
            return history

        limited_history = []
        current_chars = 0

        # 从最新的消息开始保留
        for item in reversed(history):
            if not isinstance(item, dict) or "query" not in item or "content" not in item:
                continue

            query_chars = len(item["query"])
            content_chars = len(item["content"])
            item_chars = query_chars + content_chars

            if current_chars + item_chars <= max_chars:
                limited_history.insert(0, item)
                current_chars += item_chars
            else:
                # 如果单条消息就超过限制，尝试截断
                if not limited_history and item_chars > max_chars:
                    truncated_query = self.truncate_text(item["query"], max_chars // 4)
                    truncated_content = self.truncate_text(item["content"], max_chars * 3 // 4)
                    limited_history.insert(0, {
                        "query": truncated_query,
                        "content": truncated_content
                    })
                break

        self.logger.info(f"历史消息限制: 原{len(history)}条, 保留{len(limited_history)}条, 总字符数{current_chars}")
        return limited_history
    
    def limit_knowledge(self, knowledge: str, max_chars: int) -> str:
        """
        限制知识内容的字符长度

        Args:
            knowledge: 知识内容
            max_chars: 最大字符数量

        Returns:
            限制后的知识内容
        """
        if not knowledge:
            return knowledge

        current_chars = len(knowledge)
        if current_chars <= max_chars:
            return knowledge

        # 尝试按段落截断，保持内容完整性
        paragraphs = knowledge.split('\n\n')
        limited_knowledge = ""
        current_chars = 0

        for paragraph in paragraphs:
            paragraph_chars = len(paragraph)
            if current_chars + paragraph_chars + 2 <= max_chars:  # +2 for '\n\n'
                limited_knowledge += paragraph + '\n\n'
                current_chars += paragraph_chars + 2
            else:
                # 如果单个段落就超过限制，截断该段落
                if not limited_knowledge:
                    limited_knowledge = self.truncate_text(paragraph, max_chars)
                break

        # 确保最终结果不超过限制
        final_result = limited_knowledge.strip()
        if len(final_result) > max_chars:
            final_result = self.truncate_text(final_result, max_chars)

        self.logger.info(f"知识内容限制: 原字符数{len(knowledge)}, 限制后{len(final_result)}")
        return final_result
    
    def limit_query(self, query: str, max_chars: int) -> str:
        """
        限制查询的字符长度

        Args:
            query: 查询内容
            max_chars: 最大字符数量

        Returns:
            限制后的查询内容
        """
        if not query:
            return query

        current_chars = len(query)
        if current_chars <= max_chars:
            return query

        limited_query = self.truncate_text(query, max_chars)
        self.logger.info(f"查询限制: 原字符数{current_chars}, 限制后{len(limited_query)}")
        return limited_query
    
    def limit_web_search_context(self, web_context: str, max_chars: int) -> str:
        """
        限制联网搜索上下文的字符长度

        Args:
            web_context: 联网搜索上下文
            max_chars: 最大字符数量

        Returns:
            限制后的联网搜索上下文
        """
        return self.limit_knowledge(web_context, max_chars)

    def limit_document_content(self, document: str, max_chars: int) -> str:
        """
        限制文档内容的字符长度

        Args:
            document: 文档内容
            max_chars: 最大字符数量

        Returns:
            限制后的文档内容
        """
        return self.limit_knowledge(document, max_chars)
    
    def calculate_total_chars(self, messages: List[Dict]) -> int:
        """
        计算消息列表的总字符数量

        Args:
            messages: OpenAI格式的消息列表

        Returns:
            总字符数量
        """
        total_chars = 0
        for message in messages:
            if isinstance(message, dict) and "content" in message:
                total_chars += len(message["content"])
        return total_chars
    
    def limit_messages_for_rag_qa(
        self,
        query: str,
        history: List[Dict],
        knowledge: str,
        max_total_chars: Optional[int] = None
    ) -> Tuple[str, List[Dict], str]:
        """
        为RAG问答服务限制消息内容
        适用于carqa、iscqa、ragqa、allqa、dataqa
        只有在总字数超过限制时才进行限制

        Args:
            query: 用户查询
            history: 历史消息
            knowledge: 检索到的知识
            max_total_chars: 最大总字符数，默认使用实例的max_chars

        Returns:
            (限制后的query, 限制后的history, 限制后的knowledge)
        """
        if max_total_chars is None:
            max_total_chars = self.max_chars

        # 计算当前总字符数
        query_chars = len(query) if query else 0
        history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                           for item in history if isinstance(item, dict))
        knowledge_chars = len(knowledge) if knowledge else 0
        total_chars = query_chars + history_chars + knowledge_chars

        # 如果总字数未超过限制，直接返回原内容
        if total_chars <= max_total_chars:
            self.logger.info(f"RAG问答总字符数{total_chars}未超过限制{max_total_chars}，无需限制")
            return query, history, knowledge

        # 超过限制时，为系统prompt预留字符数
        system_prompt_chars = 1000
        available_chars = max_total_chars - system_prompt_chars

        # 分配字符数比例：query(10%), history(30%), knowledge(60%)
        query_max_chars = int(available_chars * 0.1)
        history_max_chars = int(available_chars * 0.3)
        knowledge_max_chars = int(available_chars * 0.6)

        # 限制各部分内容
        limited_query = self.limit_query(query, query_max_chars)
        limited_history = self.limit_history(history, history_max_chars)
        limited_knowledge = self.limit_knowledge(knowledge, knowledge_max_chars)

        final_query_chars = len(limited_query)
        final_history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                                 for item in limited_history if isinstance(item, dict))
        final_knowledge_chars = len(limited_knowledge)

        self.logger.info(f"RAG问答字符数限制完成: 原总数{total_chars} -> 限制后{final_query_chars + final_history_chars + final_knowledge_chars}, "
                        f"query({final_query_chars}), history({final_history_chars}), knowledge({final_knowledge_chars})")

        return limited_query, limited_history, limited_knowledge
    
    def limit_messages_for_llm_qa(
        self,
        query: str,
        history: List[Dict],
        web_context: str = "",
        max_total_chars: Optional[int] = None
    ) -> Tuple[str, List[Dict], str]:
        """
        为LLM问答服务限制消息内容
        只有在总字数超过限制时才进行限制

        Args:
            query: 用户查询
            history: 历史消息
            web_context: 联网搜索上下文
            max_total_chars: 最大总字符数，默认使用实例的max_chars

        Returns:
            (限制后的query, 限制后的history, 限制后的web_context)
        """
        if max_total_chars is None:
            max_total_chars = self.max_chars

        # 计算当前总字符数
        query_chars = len(query) if query else 0
        history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                           for item in history if isinstance(item, dict))
        web_context_chars = len(web_context) if web_context else 0
        total_chars = query_chars + history_chars + web_context_chars

        # 如果总字数未超过限制，直接返回原内容
        if total_chars <= max_total_chars:
            self.logger.info(f"LLM问答总字符数{total_chars}未超过限制{max_total_chars}，无需限制")
            return query, history, web_context

        # 超过限制时，为系统prompt预留字符数
        system_prompt_chars = 1000
        available_chars = max_total_chars - system_prompt_chars

        if web_context:
            # 有联网搜索：query(10%), history(40%), web_context(50%)
            query_max_chars = int(available_chars * 0.1)
            history_max_chars = int(available_chars * 0.4)
            web_context_max_chars = int(available_chars * 0.5)
        else:
            # 无联网搜索：query(20%), history(80%)
            query_max_chars = int(available_chars * 0.2)
            history_max_chars = int(available_chars * 0.8)
            web_context_max_chars = 0

        # 限制各部分内容
        limited_query = self.limit_query(query, query_max_chars)
        limited_history = self.limit_history(history, history_max_chars)
        limited_web_context = self.limit_web_search_context(web_context, web_context_max_chars)

        final_query_chars = len(limited_query)
        final_history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                                 for item in limited_history if isinstance(item, dict))
        final_web_context_chars = len(limited_web_context)

        self.logger.info(f"LLM问答字符数限制完成: 原总数{total_chars} -> 限制后{final_query_chars + final_history_chars + final_web_context_chars}, "
                        f"query({final_query_chars}), history({final_history_chars}), web_context({final_web_context_chars})")

        return limited_query, limited_history, limited_web_context
    
    def limit_messages_for_doc_qa(
        self,
        query: str,
        history: List[Dict],
        document: str,
        max_total_chars: Optional[int] = None
    ) -> Tuple[str, List[Dict], str]:
        """
        为文档问答服务限制消息内容
        只有在总字数超过限制时才进行限制

        Args:
            query: 用户查询
            history: 历史消息
            document: 文档内容
            max_total_chars: 最大总字符数，默认使用实例的max_chars

        Returns:
            (限制后的query, 限制后的history, 限制后的document)
        """
        if max_total_chars is None:
            max_total_chars = self.max_chars

        # 计算当前总字符数
        query_chars = len(query) if query else 0
        history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                           for item in history if isinstance(item, dict))
        document_chars = len(document) if document else 0
        total_chars = query_chars + history_chars + document_chars

        # 如果总字数未超过限制，直接返回原内容
        if total_chars <= max_total_chars:
            self.logger.info(f"文档问答总字符数{total_chars}未超过限制{max_total_chars}，无需限制")
            return query, history, document

        # 超过限制时，为系统prompt预留字符数
        system_prompt_chars = 1000
        available_chars = max_total_chars - system_prompt_chars

        # 分配字符数比例：query(10%), history(20%), document(70%)
        query_max_chars = int(available_chars * 0.1)
        history_max_chars = int(available_chars * 0.2)
        document_max_chars = int(available_chars * 0.7)

        # 限制各部分内容
        limited_query = self.limit_query(query, query_max_chars)
        limited_history = self.limit_history(history, history_max_chars)
        limited_document = self.limit_document_content(document, document_max_chars)

        final_query_chars = len(limited_query)
        final_history_chars = sum(len(item.get('query', '') + item.get('content', ''))
                                 for item in limited_history if isinstance(item, dict))
        final_document_chars = len(limited_document)

        self.logger.info(f"文档问答字符数限制完成: 原总数{total_chars} -> 限制后{final_query_chars + final_history_chars + final_document_chars}, "
                        f"query({final_query_chars}), history({final_history_chars}), document({final_document_chars})")

        return limited_query, limited_history, limited_document
