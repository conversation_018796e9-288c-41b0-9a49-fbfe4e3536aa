# !/usr/bin/env python 
#-*- coding: utf-8 -*- 
import base64
import json
import requests
import warnings
from urllib3.exceptions import InsecureRequestWarning
warnings.simplefilter('ignore', InsecureRequestWarning)
import redis 

def kc_decode(data, kc_domain='localhost'):
    # 获取密码
    url = f'https://{kc_domain}:19988/keycenter/v2/masterkey/sid/mmc/decrypt'
    # 定义请求头
    headers = {
        'Accept': 'application/json',
        'Content-type': 'application/json'
    }
    response = requests.post(url, headers=headers, auth=None, data=json.dumps({
        'data': data ,
        'compress': 0
    }), verify=False)

    res = response.json()
    password = base64.b64decode(res['plaintext']).decode('utf-8')
    return password

if __name__ == '__main__':

    data = "GCA2l2qb3VTRYAm43uxIoAju8LD7po+djL0wPkN+hWepDxgSYDT9BlOXShmabnN+KHtyDDX/GBADfabZs9RHs6po1Yr+ruPRGBROBneCJeRjOKwXZR1G+YNP48NZ7gA="  # 填入 平台上的加密密码

    #这里替换为连接的实例 host 和 port 
    host = 'wcc.cache01.test.b2c.srv' 
    port = 22122 
    kc_domain = 'kcagent.sec.xiaomi.com'
    #这里替换为实例 ID 和实例 password 
    pwd= kc_decode(data, kc_domain)
    #连接时通过 password 参数指定 AUTH 信息，由 user,pwd 通过"@"拼接而成 

    r = redis.StrictRedis(host=host, port=port, password=pwd) 
    #连接建立后就可以进行数据库操作，请参见 https://github.com/andymccurdy/redis-py 
    r.set('xiaomi', 'wuhan')

    print(r.get('xiaomi'))