"""
测试新的235B模型集成
验证混合思考模型、纯思考模型和非思考模型的正确工作
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from core.llm_provider import get_llm_provider
from config.model_config import (
    get_model_type, 
    is_hybrid_thinking_model, 
    is_pure_thinking_model, 
    is_non_thinking_model,
    resolve_235b_model
)
from pipelines.llm_qa import LLMQA

def test_model_type_detection():
    """测试模型类型检测功能"""
    print("=== 测试模型类型检测 ===")
    
    # 测试混合思考模型
    assert is_hybrid_thinking_model("qwen3_32b"), "qwen3_32b应该是混合思考模型"
    assert is_hybrid_thinking_model("qwen3_8b"), "qwen3_8b应该是混合思考模型"
    print("✓ 混合思考模型检测正确")
    
    # 测试纯思考模型
    assert is_pure_thinking_model("qwen3_235b_thinking"), "qwen3_235b_thinking应该是纯思考模型"
    print("✓ 纯思考模型检测正确")
    
    # 测试非思考模型
    assert is_non_thinking_model("qwen3_235b_instruct"), "qwen3_235b_instruct应该是非思考模型"
    print("✓ 非思考模型检测正确")
    
    # 测试235B模型解析
    assert resolve_235b_model(True) == "qwen3_235b_thinking", "enable_thinking=True应该选择思考模型"
    assert resolve_235b_model(False) == "qwen3_235b_instruct", "enable_thinking=False应该选择非思考模型"
    print("✓ 235B模型解析正确")

def test_provider_creation():
    """测试Provider创建功能"""
    print("\n=== 测试Provider创建 ===")
    
    # 测试混合思考模型Provider创建
    provider_32b = get_llm_provider("qwen3_32b", "test_req_001")
    print(f"✓ 创建qwen3_32b Provider: {provider_32b.__class__.__name__}")
    
    # 测试235B模型动态选择
    provider_235b_thinking = get_llm_provider("qwen3_235b_2507", "test_req_002", enable_thinking=True)
    print(f"✓ 创建qwen3_235b_2507 (thinking) Provider: {provider_235b_thinking.__class__.__name__}")
    
    provider_235b_instruct = get_llm_provider("qwen3_235b_2507", "test_req_003", enable_thinking=False)
    print(f"✓ 创建qwen3_235b_2507 (instruct) Provider: {provider_235b_instruct.__class__.__name__}")
    
    # 验证Provider类型
    assert provider_235b_thinking.__class__.__name__ == "QWEN3_235B_Thinking_Provider"
    assert provider_235b_instruct.__class__.__name__ == "QWEN3_235B_Instruct_Provider"
    print("✓ Provider类型验证正确")

def test_pipeline_integration():
    """测试Pipeline集成"""
    print("\n=== 测试Pipeline集成 ===")
    
    # 测试LLMQA与235B模型集成
    qa_235b = LLMQA(model_id="qwen3_235b_2507", request_id="test_req_004")
    print(f"✓ 创建LLMQA实例，模型ID: {qa_235b.model_id}")
    
    # 测试动态Provider获取
    provider_thinking = qa_235b._get_provider(enable_thinking=True)
    provider_instruct = qa_235b._get_provider(enable_thinking=False)
    
    assert provider_thinking.__class__.__name__ == "QWEN3_235B_Thinking_Provider"
    assert provider_instruct.__class__.__name__ == "QWEN3_235B_Instruct_Provider"
    print("✓ Pipeline动态Provider获取正确")

async def test_mock_stream_output():
    """模拟测试流式输出格式（不实际调用API）"""
    print("\n=== 模拟测试流式输出格式 ===")
    
    # 模拟不同类型模型的输出格式
    def mock_hybrid_output():
        """模拟混合思考模型输出"""
        return [
            {'type': 'reasoning', 'content': '我需要思考一下...', 'role': 'assistant', 'finish_reason': ''},
            {'type': 'content', 'content': '这是回答内容', 'role': 'assistant', 'finish_reason': 'stop'}
        ]
    
    def mock_pure_thinking_output():
        """模拟纯思考模型输出"""
        return [
            {'type': 'reasoning', 'content': '思考过程...', 'role': 'assistant', 'finish_reason': ''},
            {'type': 'content', 'content': '回答内容', 'role': 'assistant', 'finish_reason': 'stop'}
        ]
    
    def mock_non_thinking_output():
        """模拟非思考模型输出"""
        return [
            {'type': 'content', 'content': '直接回答内容', 'role': 'assistant', 'finish_reason': 'stop'}
        ]
    
    # 验证输出格式一致性
    hybrid_output = mock_hybrid_output()
    pure_thinking_output = mock_pure_thinking_output()
    non_thinking_output = mock_non_thinking_output()
    
    # 所有输出都应该包含content类型
    assert any(chunk['type'] == 'content' for chunk in hybrid_output)
    assert any(chunk['type'] == 'content' for chunk in pure_thinking_output)
    assert any(chunk['type'] == 'content' for chunk in non_thinking_output)
    
    # 思考模型应该包含reasoning类型
    assert any(chunk['type'] == 'reasoning' for chunk in hybrid_output)
    assert any(chunk['type'] == 'reasoning' for chunk in pure_thinking_output)
    
    # 非思考模型不应该包含reasoning类型
    assert not any(chunk['type'] == 'reasoning' for chunk in non_thinking_output)
    
    print("✓ 流式输出格式验证正确")

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试不支持的模型ID
    try:
        get_llm_provider("unsupported_model")
        assert False, "应该抛出异常"
    except ValueError as e:
        assert "不支持的模型ID" in str(e)
        print("✓ 不支持模型ID的错误处理正确")
    
    # 测试不存在的模型类型
    try:
        from config.model_config import get_model_type
        get_model_type("nonexistent_model")
        assert False, "应该抛出异常"
    except ValueError as e:
        assert "不支持的模型ID" in str(e)
        print("✓ 不存在模型的错误处理正确")

def main():
    """运行所有测试"""
    print("开始测试新模型集成...")
    
    try:
        test_model_type_detection()
        test_provider_creation()
        test_pipeline_integration()
        asyncio.run(test_mock_stream_output())
        test_error_handling()
        
        print("\n🎉 所有测试通过！新模型集成成功！")
        
        print("\n=== 使用说明 ===")
        print("1. 混合思考模型 (qwen3_32b, qwen3_8b):")
        print("   - enable_thinking=True: 输出reasoning_content")
        print("   - enable_thinking=False: 在用户消息后添加/no_think，不输出reasoning_content")
        
        print("\n2. 235B模型 (qwen3_235b_2507):")
        print("   - enable_thinking=True: 调用Qwen3-235B-A22B-Thinking-2507，总是输出reasoning_content")
        print("   - enable_thinking=False: 调用Qwen3-235B-A22B-Instruct-2507，不输出reasoning_content")
        
        print("\n3. 输出格式:")
        print("   - 所有模型的输出格式保持一致")
        print("   - type字段: 'content' | 'reasoning' | 'error'")
        print("   - 思考模型会有reasoning类型的输出，非思考模型只有content类型")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
