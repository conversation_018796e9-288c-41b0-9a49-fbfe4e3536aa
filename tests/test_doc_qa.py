import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from pipelines.doc_qa import DocQA
import pytest

@pytest.mark.asyncio
async def test_docqa_single_turn():
    """测试DocQA单轮对话"""
    docqa = DocQA(model_id="qwen3_32b", request_id="doc_qa_single")
    query = "根据文档提个问题"
    user_id = "test_user_single"
    conversation_id = "test-123"
    history = []
    result = await docqa.generate(query, user_id, conversation_id, history)
    print("单轮对话结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_docqa_multi_turn():
    """测试DocQA多轮对话"""
    docqa = DocQA(model_id="qwen3_32b", request_id="doc_qa_multi")
    user_id = "test_user_multi"
    conversation_id = "test-123"
    history = [
        {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
        {"query": "FPC点胶规范", "content": "点胶规范是..."}
    ]
    query = "点胶厚度要求是多少？"
    result = await docqa.generate(query, user_id, conversation_id, history)
    print("多轮对话结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_docqa_stream_single_turn():
    """测试DocQA单轮对话流式输出"""
    docqa = DocQA(model_id="qwen3_32b", request_id="doc_qa_stream_single")
    query = "根据文档提个问题"
    user_id = "test_user_stream_single"
    conversation_id = "test-123"
    history = []
    chunks = []
    async for chunk in docqa.generate_stream(query, user_id, conversation_id, history):
        print("流式单轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "content" in chunk or "choices" in chunk

@pytest.mark.asyncio
async def test_docqa_stream_multi_turn():
    """测试DocQA多轮对话流式输出"""
    docqa = DocQA(model_id="qwen3_32b", request_id="doc_qa_stream_multi")
    user_id = "test_user_stream_multi"
    conversation_id = "test-123"
    history = [
        {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
        {"query": "FPC点胶规范", "content": "点胶规范是..."}
    ]
    query = "点胶厚度要求是多少？"
    chunks = []
    async for chunk in docqa.generate_stream(query, user_id, conversation_id, history):
        print("流式多轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "content" in chunk or "choices" in chunk

if __name__ == "__main__":
    # asyncio.run(test_docqa_single_turn())
    # asyncio.run(test_docqa_multi_turn())
    asyncio.run(test_docqa_stream_single_turn())
    asyncio.run(test_docqa_stream_multi_turn()) 