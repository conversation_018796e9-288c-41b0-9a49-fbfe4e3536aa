#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索API接口的脚本
测试新的集合选择功能
"""

import sys
import os
import asyncio
import json
import uuid
import argparse
import httpx
import traceback
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
if os.path.exists(env_file):
    load_dotenv(env_file)
    print(f"已加载{env_type}环境配置: {env_file}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    if os.path.exists('.env'):
        load_dotenv('.env')
        print("已加载默认环境配置: .env")
    else:
        print(f"警告: 环境配置文件不存在: {env_file} 或 .env")

# 默认API地址
DEFAULT_API_URL = "http://localhost:8080/api/v1"

# 从环境变量获取API访问令牌
def get_api_token():
    """获取API访问令牌"""
    token = os.getenv("API_ACCESS_TOKEN", "")
    if token:
        return f"Bear {token}"
    return ""


async def test_search_all_collections(api_url: str, timeout: float = 600.0) -> None:
    """测试搜索所有集合（默认行为）"""
    print("\n===== 测试搜索所有集合 =====")
    
    url = f"{api_url}/search"
    request_id = str(uuid.uuid4())
    
    payload = {
        "query": "复合应力导致 FPC 断线案例",
        "user_id": "test_user",
        "msg_id": request_id,
        "top_k": 5,
        "top_r": 3,
        "min_score": 0.3
        # 不指定collections，应该搜索所有集合
    }
    
    print(f"请求URL: {url}")
    print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    # 获取API令牌
    api_token = get_api_token()
    headers = {"Authorization": api_token} if api_token else {}
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, json=payload, headers=headers)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除"data: "前缀
                        try:
                            result = json.loads(data)
                            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"无法解析的数据: {data}")
            else:
                print(f"请求失败: {response.text}")
                
    except Exception as e:
        print(f"测试失败: {str(e)}")
        traceback.print_exc()


async def test_search_specific_collections(api_url: str, timeout: float = 600.0) -> None:
    """测试搜索指定集合"""
    print("\n===== 测试搜索指定集合 =====")
    
    url = f"{api_url}/search"
    
    # 测试不同的集合组合
    test_cases = [
        {
            "name": "只搜索汽车知识库",
            "query": "汽车发动机故障诊断",
            "collections": ["car"]
        },
        {
            "name": "只搜索硬工知识库",
            "query": "PCB设计规范",
            "collections": ["hardware"]
        },
        {
            "name": "只搜索数据库",
            "query": "项目进度查询",
            "collections": ["data"]
        },
        {
            "name": "搜索汽车和硬工知识库",
            "query": "电子控制单元设计",
            "collections": ["car", "hardware"]
        },
        {
            "name": "搜索数据和硬工知识库",
            "query": "产品质量分析",
            "collections": ["data", "hardware"]
        },
        {
            "name": "搜索所有集合",
            "query": "技术文档检索",
            "collections": ["car", "hardware", "data"]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        
        request_id = str(uuid.uuid4())
        payload = {
            "query": test_case["query"],
            "user_id": "test_user",
            "msg_id": request_id,
            "top_k": 3,
            "top_r": 2,
            "min_score": 0.3,
            "collections": test_case["collections"]
        }
        
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        # 获取API令牌
        api_token = get_api_token()
        headers = {"Authorization": api_token} if api_token else {}
        
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(url, json=payload, headers=headers)
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 处理流式响应
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]  # 移除"data: "前缀
                            try:
                                result = json.loads(data)
                                print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                            except json.JSONDecodeError:
                                print(f"无法解析的数据: {data}")
                else:
                    print(f"请求失败: {response.text}")
                    
        except Exception as e:
            print(f"测试失败: {str(e)}")
            traceback.print_exc()


async def test_search_invalid_collections(api_url: str, timeout: float = 600.0) -> None:
    """测试无效集合类型"""
    print("\n===== 测试无效集合类型 =====")
    
    url = f"{api_url}/search"
    request_id = str(uuid.uuid4())
    
    payload = {
        "query": "测试查询",
        "user_id": "test_user",
        "msg_id": request_id,
        "top_k": 3,
        "collections": ["invalid_type", "unknown_collection"]
    }
    
    print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    # 获取API令牌
    api_token = get_api_token()
    headers = {"Authorization": api_token} if api_token else {}
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, json=payload, headers=headers)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除"data: "前缀
                        try:
                            result = json.loads(data)
                            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"无法解析的数据: {data}")
            else:
                print(f"请求失败: {response.text}")
                
    except Exception as e:
        print(f"测试失败: {str(e)}")
        traceback.print_exc()


async def test_search_empty_collections(api_url: str, timeout: float = 600.0) -> None:
    """测试空集合列表"""
    print("\n===== 测试空集合列表 =====")
    
    url = f"{api_url}/search"
    request_id = str(uuid.uuid4())
    
    payload = {
        "query": "测试查询",
        "user_id": "test_user",
        "msg_id": request_id,
        "top_k": 3,
        "collections": []  # 空列表，应该回退到所有集合
    }
    
    print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
    
    # 获取API令牌
    api_token = get_api_token()
    headers = {"Authorization": api_token} if api_token else {}
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, json=payload, headers=headers)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除"data: "前缀
                        try:
                            result = json.loads(data)
                            print(f"搜索结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"无法解析的数据: {data}")
            else:
                print(f"请求失败: {response.text}")
                
    except Exception as e:
        print(f"测试失败: {str(e)}")
        traceback.print_exc()


async def test_health(api_url: str, timeout: float = 30.0) -> bool:
    """测试健康检查接口"""
    url = f"{api_url}/health"
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url)
            response.raise_for_status()
            result = response.json()
            print(f"健康检查结果: {result}")
            return True
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
        return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="搜索API测试脚本")
    parser.add_argument(
        "--api-url", 
        type=str, 
        default=DEFAULT_API_URL, 
        help=f"API地址，默认为{DEFAULT_API_URL}"
    )
    parser.add_argument(
        "--test-all", 
        action="store_true", 
        help="测试所有功能"
    )
    parser.add_argument(
        "--test-health", 
        action="store_true", 
        help="测试健康检查接口"
    )
    parser.add_argument(
        "--timeout", 
        type=float, 
        default=600.0, 
        help="请求超时时间（秒），默认为600秒（10分钟）"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何测试，则默认测试所有功能
    if not (args.test_all or args.test_health):
        args.test_all = True
    
    print("搜索API测试脚本")
    print("=" * 50)
    
    # 测试健康检查接口
    if args.test_all or args.test_health:
        health_ok = await test_health(args.api_url, args.timeout)
        if not health_ok and args.test_all:
            print("健康检查失败，跳过其他测试")
            return
    
    # 测试搜索功能
    if args.test_all:
        await test_search_all_collections(args.api_url, args.timeout)
        await test_search_specific_collections(args.api_url, args.timeout)
        await test_search_invalid_collections(args.api_url, args.timeout)
        await test_search_empty_collections(args.api_url, args.timeout)
    
    print("\n" + "=" * 50)
    print("所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
