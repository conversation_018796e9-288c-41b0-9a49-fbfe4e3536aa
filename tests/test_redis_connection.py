"""测试Redis连接
"""
import sys
import os
import redis

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from config.redis_config import get_redis_connection_params
from loguru import logger

def test_redis_connection():
    """测试Redis连接"""
    # 获取Redis连接参数
    redis_params = get_redis_connection_params()
    logger.info(f"尝试连接Redis: {redis_params['host']}:{redis_params['port']}")
    
    try:
        # 创建Redis客户端
        r = redis.StrictRedis(**redis_params)
        
        # 测试连接
        if r.ping():
            logger.info("Redis连接成功!")
            
            # 测试基本操作
            test_key = "test:connection"
            r.set(test_key, "Hello Redis")
            value = r.get(test_key)
            logger.info(f"读取测试键值: {value.decode('utf-8')}")
            r.delete(test_key)
            logger.info("测试完成，已删除测试键")
            
    except redis.exceptions.ConnectionError as e:
        logger.error(f"Redis连接失败: {e}")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")

if __name__ == "__main__":
    test_redis_connection()