#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ALLQA API 接口测试脚本
测试 all-qa API 接口的集合选择功能
"""

import asyncio
import aiohttp
import json
import sys
import os
import time
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

# API 配置
API_BASE_URL = "http://localhost:8080/api/v1"
API_TOKEN = "ipd-OOabxNh6usxsPgTt6EYZHqE1"  # 根据实际配置修改


class ALLQAAPITester:
    """ALLQA API 测试器"""
    
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def create_request_payload(
        self,
        query: str,
        collection: Optional[List[str]] = None,
        user_id: str = "test_user",
        model_id: str = "qwen3_32b",
        top_k: int = 5,
        top_r: int = 3,
        min_score: float = 0.1
    ) -> Dict:
        """创建请求负载"""
        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": f"test_msg_{int(time.time())}",
            "conversation_id": f"test_conv_{int(time.time())}",
            "history": [],
            "stream": True,
            "top_k": top_k,
            "top_r": top_r,
            "min_score": min_score,
            "enable_thinking": False,  # 关闭思考过程以加快测试
            "mode": "common"
        }
        
        if collection is not None:
            payload["collection"] = collection
        
        return payload
    
    async def test_api_call(
        self,
        query: str,
        collection: Optional[List[str]] = None,
        max_chunks: int = 5
    ) -> Dict:
        """测试 API 调用"""
        url = f"{self.base_url}/all-qa"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bear {self.token}"
        }
        
        payload = self.create_request_payload(query, collection)
        
        print(f"请求URL: {url}")
        print(f"请求负载: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            start_time = time.time()
            async with self.session.post(url, json=payload, headers=headers) as response:
                print(f"响应状态码: {response.status}")
                
                if response.status != 200:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}",
                        "chunks": []
                    }
                
                chunks = []
                chunk_count = 0
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        chunk_count += 1
                        try:
                            chunk_data = json.loads(line[6:])  # 去掉 'data: ' 前缀
                            chunks.append(chunk_data)
                            
                            chunk_type = chunk_data.get("type", "unknown")
                            content_length = len(chunk_data.get("content", ""))
                            print(f"  Chunk {chunk_count}: type={chunk_type}, content_length={content_length}")
                            
                            if chunk_count >= max_chunks:
                                print(f"  已获取 {max_chunks} 个chunk，停止接收...")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"  解析chunk失败: {e}")
                            continue
                
                end_time = time.time()
                return {
                    "success": True,
                    "chunks": chunks,
                    "chunk_count": chunk_count,
                    "duration": end_time - start_time
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chunks": []
            }


async def test_collection_configurations():
    """测试不同的集合配置"""
    print("=" * 60)
    print("测试 ALLQA API 的集合选择功能")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        {
            "name": "默认配置（所有集合）",
            "collection": None,
            "description": "不指定collection参数，使用所有可用集合"
        },
        {
            "name": "只搜索数据集合",
            "collection": ["data"],
            "description": "只在DATA_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "只搜索硬件集合",
            "collection": ["hardware"],
            "description": "只在HARDWARE_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "只搜索汽车集合",
            "collection": ["car"],
            "description": "只在CAR_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "搜索汽车和硬件集合",
            "collection": ["car", "hardware"],
            "description": "在CAR_SEARCH_COLLECTIONS和HARDWARE_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "搜索数据和汽车集合",
            "collection": ["data", "car"],
            "description": "在DATA_SEARCH_COLLECTIONS和CAR_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "搜索所有集合",
            "collection": ["data", "hardware", "car"],
            "description": "明确指定搜索所有集合"
        }
    ]
    
    query = "什么是人工智能？"
    
    async with ALLQAAPITester(API_BASE_URL, API_TOKEN) as tester:
        for i, config in enumerate(test_configs, 1):
            print(f"\n测试配置 {i}: {config['name']}")
            print(f"描述: {config['description']}")
            print(f"集合参数: {config['collection']}")
            print("-" * 40)
            
            result = await tester.test_api_call(
                query=query,
                collection=config['collection'],
                max_chunks=3  # 限制chunk数量
            )
            
            if result["success"]:
                print(f"✅ 测试成功")
                print(f"   获取chunk数量: {result['chunk_count']}")
                print(f"   耗时: {result['duration']:.2f}秒")
                
                # 分析chunk类型
                chunk_types = [chunk.get("type", "unknown") for chunk in result["chunks"]]
                print(f"   Chunk类型: {chunk_types}")
            else:
                print(f"❌ 测试失败: {result['error']}")


async def test_invalid_collections():
    """测试无效集合参数"""
    print("\n" + "=" * 60)
    print("测试无效集合参数的处理")
    print("=" * 60)
    
    invalid_configs = [
        {
            "name": "无效集合类型",
            "collection": ["invalid_type"],
            "description": "使用不存在的集合类型"
        },
        {
            "name": "混合有效和无效集合",
            "collection": ["data", "invalid_type"],
            "description": "混合有效和无效的集合类型"
        },
        {
            "name": "空字符串集合",
            "collection": [""],
            "description": "使用空字符串作为集合类型"
        }
    ]
    
    query = "测试查询"
    
    async with ALLQAAPITester(API_BASE_URL, API_TOKEN) as tester:
        for i, config in enumerate(invalid_configs, 1):
            print(f"\n无效测试 {i}: {config['name']}")
            print(f"描述: {config['description']}")
            print(f"集合参数: {config['collection']}")
            print("-" * 40)
            
            result = await tester.test_api_call(
                query=query,
                collection=config['collection'],
                max_chunks=2
            )
            
            if result["success"]:
                print(f"✅ API调用成功（应该使用默认集合或处理错误）")
                print(f"   获取chunk数量: {result['chunk_count']}")
            else:
                print(f"❌ API调用失败: {result['error']}")


async def test_api_health():
    """测试API健康状态"""
    print("\n" + "=" * 60)
    print("测试API健康状态")
    print("=" * 60)
    
    health_url = f"{API_BASE_URL}/health"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(health_url) as response:
                print(f"健康检查URL: {health_url}")
                print(f"响应状态码: {response.status}")
                
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ API服务正常运行")
                    print(f"健康状态: {json.dumps(health_data, ensure_ascii=False, indent=2)}")
                    return True
                else:
                    print(f"❌ API服务异常，状态码: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始 ALLQA API 接口测试")
    print(f"API地址: {API_BASE_URL}")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 首先检查API健康状态
    if not await test_api_health():
        print("\n❌ API服务不可用，请确保API服务正在运行")
        print("启动命令: python -m uvicorn api.app:app --host 0.0.0.0 --port 8080")
        return
    
    try:
        # 测试不同的集合配置
        await test_collection_configurations()
        
        # 测试无效集合参数
        await test_invalid_collections()
        
        print("\n" + "=" * 60)
        print("所有API测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        logger.exception("详细错误信息:")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
