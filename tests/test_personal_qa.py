#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个人知识库问答测试模块
测试PERSONALQA类的各种功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from pipelines.personal_qa import PERSONALQA
import pytest

@pytest.mark.asyncio
async def test_personalqa_single_turn():
    """测试PERSONALQA单轮对话"""
    personalqa = PERSONALQA(model_id="qwen3_32b", request_id="req_single")
    query = "个人知识库测试问题"
    user_id = "test_user_single"
    history = []
    result = await personalqa.generate(query, user_id, history)
    print("单轮对话结果:", result)
    assert hasattr(result, "choices") or hasattr(result, "content")

@pytest.mark.asyncio
async def test_personalqa_multi_turn():
    """测试PERSONALQA多轮对话"""
    personalqa = PERSONALQA(model_id="qwen3_32b", request_id="req_multi")
    user_id = "test_user_multi"
    # 模拟多轮历史
    history = [
        {"query": "什么是个人知识管理？", "content": "个人知识管理是指个人对知识的收集、整理、存储和应用..."},
        {"query": "个人知识库的作用", "content": "个人知识库可以帮助个人更好地管理和利用知识..."}
    ]
    query = "如何建立有效的个人知识库？"
    result = await personalqa.generate(query, user_id, history)
    print("多轮对话结果:", result)
    assert hasattr(result, "choices") or hasattr(result, "content")

@pytest.mark.asyncio
async def test_personalqa_stream_single_turn():
    """测试PERSONALQA单轮对话流式输出"""
    personalqa = PERSONALQA(model_id="qwen3_32b", request_id="req_stream_single")
    query = "个人知识库测试问题"
    user_id = "test_user_stream_single"
    history = []
    chunks = []
    async for chunk in personalqa.generate_stream(query, user_id, history):
        print("流式单轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    # 检查每个chunk有choices或content字段
    for chunk in chunks:
        assert "choices" in chunk or "content" in chunk or "type" in chunk

@pytest.mark.asyncio
async def test_personalqa_stream_multi_turn():
    """测试PERSONALQA多轮对话流式输出"""
    personalqa = PERSONALQA(model_id="qwen3_32b", request_id="req_stream_multi")
    user_id = "test_user_stream_multi"
    history = [
        {"query": "什么是个人知识管理？", "content": "个人知识管理是指个人对知识的收集、整理、存储和应用..."},
        {"query": "个人知识库的作用", "content": "个人知识库可以帮助个人更好地管理和利用知识..."}
    ]
    query = "如何建立有效的个人知识库？"
    chunks = []
    async for chunk in personalqa.generate_stream(query, user_id, history):
        print("流式多轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "choices" in chunk or "content" in chunk or "type" in chunk

@pytest.mark.asyncio
async def test_personalqa_with_parameters():
    """测试PERSONALQA带参数的调用"""
    personalqa = PERSONALQA(model_id="qwen3_32b", request_id="req_params")
    query = "个人知识库参数测试"
    user_id = "test_user_params"
    history = []
    result = await personalqa.generate(
        query=query,
        user_id=user_id,
        history=history,
        top_k=10,
        top_r=5,
        min_score=0.3,
        mode="strict",
        enable_thinking=True,
        temperature=0.7,
        top_p=0.9
    )
    print("带参数对话结果:", result)
    assert hasattr(result, "choices") or hasattr(result, "content")

if __name__ == "__main__":
    # asyncio.run(test_personalqa_single_turn())
    # asyncio.run(test_personalqa_multi_turn())
    asyncio.run(test_personalqa_stream_single_turn())
    asyncio.run(test_personalqa_stream_multi_turn())
    # asyncio.run(test_personalqa_with_parameters())
