#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")


import asyncio
from services.rerank_service import RerankService
from prompts.rag_qa_prompt import rag_qa_rerank_prompt

async def test_rerank_success():
    query = "复合应力导致 FPC 断线案例"
    documents = [{"id":"05035869-a4f8-4298-b139-421c6f37e543","content":"原因分析\nFPC 断线位置\n**核心结论：断线位置集中转轴盖对应位置，****FPC**** 中间区域出现裂纹，然后往两边延伸，内层存在裂纹；**  \n<table>\n<tr>\n<td>失效图片1<br/></td><td>![](boxk4Hs05v0TRFbjavmzX3Gv47g)<br/></td><td>![](boxk4bMmA62THvOd8Bf9XX5CBgc)<br/></td><td>![](boxk42KkOOhZXTSSckjjghfZxme)<br/><br/></td></tr>\n<tr>\n<td>失效图片2<br/></td><td>![](boxk4R33q51B3OJ8FlW3QpZZRof)<br/></td><td>![](boxk4E8hlkBCIrhuJ0Q9oPOyoPf)<br/></td><td>![](boxk4mTQKJMq7qOOJqd2O6O27ah)<br/></td></tr>\n<tr>\n<td>失效图片2<br/></td><td>![](boxk4xvPMA33VKh2sduPPrDtIyf)<br/></td><td>![](boxk4HGjzM0hV4afT8XjXXhQAqe)<br/><br/></td><td>![](boxk4OHhcWKiCiRBUy6fNoBOneh)<br/><br/></td></tr>\n</table>  \nFPC 单体对应转轴盖位置可见明显明显裂纹：  \n- 10030：Filp 侧 FPC 转轴盖位置裂纹，裂纹位置位于表层 FPC 中间区域，内层边缘可见裂纹\n- 10028：FPC 已完全断裂，断裂位置与转轴盖位置重合\n- 10027：FILP 侧及 BASE 侧转轴盖位置都存在裂纹，裂纹位置位于表层 FPC 中间区域，内层存在裂纹\n- 10034：Filp 侧 FPC 转轴盖位置裂纹，裂纹位置位于表层 FPC 中间区域","user_id":"pcb_kb_user","metadata_json":{"Header 1":"原因分析","Header 2":"FPC 断线位置","id":"20902","doc_type":"sheet","publish_time":"2025-04-28 19:03:34","project_area":"011","doc_url":"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g","doc_name":"FPC板厂工艺时间表","tm":"2025-05-06 19:23:42","importance":1.0},"score":0.03131881575727918},{"id":"c52aed58-c228-4b40-85dd-e77983c46adf","content":"FPC 点击微动失效原因汇总-20230329\n<table>\n<tr>\n<td>项目<br/></td><td>项目<br/></td><td>失效现象<br/></td><td>根因分类<br/></td><td>根因原因说明<br/></td><td>设计规则<br/></td><td>飞书链接<br/></td><td>失效图片<br/></td><td>规范名称<br/></td></tr>\n<tr>\n<td>1<br/></td><td>L1<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1 FPC弯折区上下重叠2 弯折区有结构凸台3 PU电池盖强度弱，FPC点击微动风险高<br/></td><td>FPC弯折区禁止上下重叠PU电池盖机型 FPC推荐走到电池底<br/></td><td>L1 售后黑屏问题-----结构复盘验证 <br/></td><td>[map[id:1 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n<tr>\n<td>2<br/></td><td>L12<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1，FPC长度太长2，FPC在电池上方。点击微动风险高<br/></td><td>长度设计合理FPC在电池上方。推荐走电池下方<br/></td><td>FPC连接器测试专项（FPC点击微动） <br/></td><td>[map[id:2 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n<tr>\n<td>3<br/></td><td>L81<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1，FPC长度太长2 EMI与背胶距离弯折区太近3 FPC弯折区太硬4 弯折区缺少预弯折<br/></td><td>1 FPC长度/EMI和背胶距离弯折区的距离要求2 增加预弯折，3 减短FPC1.5mm，开槽处理<br/></td><td>L81A_点击微动问题总汇（PCB） <br/></td><td>[map[id:3 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n<tr>\n<td>4<br/></td><td>L9S<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>同轴线直接压fpc,FPC有点击微动失效风险<br/></td><td>弯折区禁止有干涉点击微动风险处同轴线禁止直接压FPC<br/></td><td>L9S主FPC点击微动测试Fail问题跟踪 <br/></td><td>[map[id:4 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n<tr>\n<td>5<br/></td><td>M20<br/></td><td>BTB锡裂<br/></td><td>测试点击位置规范问题<br/></td><td>BTB连接器边缘无法承受点击测试要求<br/></td><td>点击位置规范，点击FPC弯折区中心<br/></td><td>FPC连接器测试专项（FPC点击微动） <br/></td><td>[map[id:5 type:embed-image]]<br/></td><td>点击微动点位选取 <br/></td></tr>\n<tr>\n<td>6<br/></td><td>M2<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1 FPC弯折区上下重叠2 弯折区有结构凸台3 FPC在电池上方，FPC点击微动风险高<br/></td><td>1 FPC弯折区禁止上下重叠2 FPC推荐走到电池底<br/></td><td>M2 FPC断线问题及后续策略 <br/></td><td>[map[id:6 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 FPC预弯折规则梳理 <br/></td></tr>\n<tr>\n<td>7<br/></td><td>M16T<br/></td><td>BTB本体裂开<br/></td><td>测试点击位置规范问题<br/></td><td>BTB连接器边缘无法承受点击测试要求<br/></td><td>点击位置规范，点击FPC弯折区中心<br/></td><td>M16T比亚迪点击微动失效原因 比亚迪测试方式先点击测试点1、2、3再点击测试点4、5，距4号位较近的3号位重复受力，导致BTB受力开裂<br/></td><td>[map[id:9 type:embed-image]]<br/></td><td>点击微动点位选取 <br/></td></tr>\n<tr>\n<td>8<br/></td><td>M81<br/></td><td>FPC断线<br/></td><td>测试点击位置规范问题<br/></td><td>FPC测试点击位置太近<br/></td><td>FPC点击位置太近，要求15MM以上<br/></td><td>M81 & M82 USB FPC 点击微动Fail 问题跟踪 <br/></td><td><nil><br/></td><td>点击微动点位选取 <br/></td></tr>\n<tr>\n<td>9<br/></td><td>M82<br/></td><td>FPC断线<br/></td><td>测试点击位置规范问题<br/></td><td>FPC测试点击位置太近<br/></td><td>FPC点击位置太近，要求15MM以上<br/></td><td>M81 & M82 USB FPC 点击微动Fail 问题跟踪 <br/></td><td><nil><br/></td><td>点击微动点位选取 <br/></td></tr>\n<tr>\n<td>10<br/></td><td>M1<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1，FPC长度太长2，FPC在BOX处，结构件有点击微动风险3 FPC走在电池上方<br/></td><td>1 电池上长度设计合理2 有点击微动风险地方需要加结构支撑3 FPC推荐走到电池下方<br/></td><td>M1-FPC点击微动测试问题跟进： <br/></td><td>[map[id:8 type:embed-image]]<br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n<tr>\n<td>11<br/></td><td>M9<br/></td><td>BTB锡裂<br/></td><td>测试点击位置规范问题<br/></td><td>BTB连接器边缘无法承受点击测试要求<br/></td><td>点击位置规范，点击FPC弯折区中心<br/></td><td>M9  FPC 点击微动Fail 问题跟踪 <br/></td><td><nil><br/></td><td>点击微动点位选取 <br/></td></tr>\n<tr>\n<td>12<br/></td><td>M18<br/></td><td>FPC断线<br/></td><td>设计问题<br/></td><td>1. FPC长度太长，实际装机形态和3D图不一致2. FPC走在电池上方，3D图中电池尺寸按最大值设计，实际电池要小，和弯折区有间隙3. 3层FPC，弯折区太硬，出弯折区位置拱起，贴到电池盖<br/></td><td><nil><br/></td><td>M18点击微动失效分析报告 M18 FPC点击微动记录 M18 FPC微动风险排查 <br/></td><td><br/></td><td>FPC可靠性设计checklist_V2.0 <br/></td></tr>\n</table>","user_id":"pcb_kb_user","metadata_json":{"Header 1":"FPC 点击微动失效原因汇总-20230329","id":"20902","doc_type":"sheet","publish_time":"2025-04-28 19:03:34","project_area":"011","doc_url":"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g","doc_name":"FPC板厂工艺时间表","tm":"2025-05-06 19:23:42","importance":1.0},"score":0.03131881575727918}]
    service = RerankService()
    results = await service.rerank_with_prompt(query, documents,rag_qa_rerank_prompt, top_r=3, min_score=0.0)
    print("\ntest_rerank_success results:", results)
    assert isinstance(results, list)
    payload_instruction = rag_qa_rerank_prompt.strip()
    assert len(payload_instruction) > 0

async def test_rerank_empty():
    service = RerankService()
    results = await service.rerank_with_prompt("测试问题", [], rag_qa_rerank_prompt, top_r=2)
    print("test_rerank_empty results:", results)
    assert results == []

if __name__ == "__main__":
    asyncio.run(test_rerank_success())
    asyncio.run(test_rerank_empty())