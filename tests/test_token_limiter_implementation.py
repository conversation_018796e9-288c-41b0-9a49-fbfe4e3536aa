#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证基于不同模型的输入字数限制功能实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
 # 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

def test_model_config():
    """测试模型配置"""
    print("=== 验证模型配置 ===")
    
    try:
        from config.model_config import get_model_max_input_chars, MODEL_CONFIG
        
        # 检查配置是否包含max_input_chars
        for model_id, config in MODEL_CONFIG.items():
            max_chars = config.get('max_input_chars')
            print(f"模型 {model_id}: max_input_chars = {max_chars}")
        
        print("\n--- 测试get_model_max_input_chars函数 ---")
        
        # 测试qwen3_32b (应该是12万字)
        max_chars_32b = get_model_max_input_chars("qwen3_32b")
        print(f"qwen3_32b: {max_chars_32b} (期望: 120000)")
        
        # 测试qwen3_235b_2507 (应该映射到qwen3_235b_thinking，30万字)
        max_chars_235b = get_model_max_input_chars("qwen3_235b_2507")
        print(f"qwen3_235b_2507: {max_chars_235b} (期望: 300000)")
        
        # 测试qwen3_235b_thinking (应该是30万字)
        max_chars_thinking = get_model_max_input_chars("qwen3_235b_thinking")
        print(f"qwen3_235b_thinking: {max_chars_thinking} (期望: 300000)")
        
        print("✓ 模型配置测试通过")
        
    except Exception as e:
        print(f"✗ 模型配置测试失败: {e}")
        return False
    
    return True


def test_pipeline_imports():
    """测试pipeline导入"""
    print("\n=== 验证Pipeline导入 ===")
    
    pipelines = [
        "pipelines.llm_qa",
        "pipelines.rag_qa", 
        "pipelines.doc_qa",
        "pipelines.car_qa",
        "pipelines.all_qa",
        "pipelines.data_qa",
        "pipelines.isc_qa",
        "pipelines.personal_qa"
    ]
    
    success_count = 0
    for pipeline in pipelines:
        try:
            __import__(pipeline)
            print(f"✓ {pipeline} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {pipeline} 导入失败: {e}")
    
    print(f"\n导入成功: {success_count}/{len(pipelines)}")
    return success_count == len(pipelines)


def test_character_limiter():
    """测试字符限制器"""
    print("\n=== 验证字符限制器 ===")
    
    try:
        from utils.token_limiter import CharacterLimiter
        
        # 测试不同的max_chars设置
        limiter_120k = CharacterLimiter(max_chars=120000)
        limiter_300k = CharacterLimiter(max_chars=300000)
        
        print(f"120K限制器: {limiter_120k.max_chars}")
        print(f"300K限制器: {limiter_300k.max_chars}")
        
        # 测试字符限制功能
        test_query = "测试查询" * 2000
        test_history = [{"query": "历史查询" * 5000, "content": "历史回答" * 5000}]
        test_knowledge = "测试知识" * 30000
        
        
        total_chars_ori = len(test_query) + sum(len(item['query'] + item['content']) for item in test_history) + len(test_knowledge)
        
        print(f"原始数据长度:")
        print(f"  查询: {len(test_query)} 字符")
        print(f"  历史: {sum(len(item['query'] + item['content']) for item in test_history)} 字符")
        print(f"  知识: {len(test_knowledge)} 字符")
        print(f"  总计: {total_chars_ori} 字符")
        
        limited_query, limited_history, limited_knowledge = limiter_120k.limit_messages_for_llm_qa(
            test_query, test_history, test_knowledge
        )
        total_chars = len(limited_query) + sum(len(item['query'] + item['content']) for item in limited_history) + len(limited_knowledge)
        print(f"限制后的数据长度:")
        print(f"  查询: {len(limited_query)} 字符")
        print(f"  历史: {sum(len(item['query'] + item['content']) for item in limited_history)} 字符")
        print(f"  知识: {len(limited_knowledge)} 字符")
        print(f"  总计: {total_chars} 字符")
        
        limited_query, limited_history, limited_knowledge = limiter_300k.limit_messages_for_llm_qa(
            test_query, test_history, test_knowledge
        )
        total_chars = len(limited_query) + sum(len(item['query'] + item['content']) for item in limited_history) + len(limited_knowledge)
        print(f"300K限制后总字符数: {total_chars} (应该 <= 300000)")
        
        print("✓ 字符限制器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 字符限制器测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始验证基于模型的输入字数限制功能实现...\n")
    
    results = []
    results.append(test_model_config())
    results.append(test_pipeline_imports())
    results.append(test_character_limiter())
    
    print(f"\n=== 验证结果 ===")
    print(f"通过测试: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ 所有测试通过！实现成功。")
        print("\n功能说明:")
        print("1. qwen3_235b_2507 最大输入字数: 30万字")
        print("2. qwen3_32b 最大输入字数: 12万字")
        print("3. 所有pipeline都已更新为基于模型动态设置字符限制")
        print("4. 字符限制器会根据不同模型自动调整最大字符数")
    else:
        print("✗ 部分测试失败，请检查实现。")