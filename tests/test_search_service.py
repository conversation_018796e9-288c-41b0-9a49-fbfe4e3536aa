#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

import asyncio
from services.search_service import SearchService

async def test_search_success():
    service = SearchService()
    user_id = "test_user"
    query = "复合应力导致 FPC 断线案例"
    top_k = 3
    results, error = await service.search(user_id, query, top_k)
    print("test_search_success results:", results)
    print("test_search_success error:", error)
    assert error is None or isinstance(results, list)

async def test_search_empty():
    service = SearchService()
    results, error = await service.search("test_user", "", 3)
    print("test_search_empty results:", results)
    print("test_search_empty error:", error)
    assert error is None or isinstance(results, list)

if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(test_search_success())
        loop.run_until_complete(test_search_empty())
        print("所有测试通过！")
    except AssertionError as e:
        print("测试失败：", e)
        exit(1)
