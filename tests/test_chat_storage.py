"""对话历史存储类的测试用例
"""
import sys
import os
import unittest
import time
import json
import uuid
import redis

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from services.chat_storage_service import ConversationHistoryStorage
from config.redis_config import get_redis_connection_params
from config.logging_config import configure_logging
from loguru import logger

# 配置日志
configure_logging()

class TestRedisConversationStorage(unittest.TestCase):
    """使用Redis服务器的ConversationHistoryStorage测试用例"""
    
    @classmethod
    def setUpClass(cls):
        """在所有测试开始前设置测试环境"""
        # 获取Redis连接参数
        redis_params = get_redis_connection_params()
        logger.info(f"尝试连接Redis服务器: {redis_params['host']}:{redis_params['port']}")
        
        try:
            # 创建Redis客户端
            cls.redis_client = redis.StrictRedis(**redis_params)
            # 测试连接
            cls.redis_client.ping()
            logger.info("成功连接到Redis服务器")
            
            # 创建ConversationHistoryStorage实例
            cls.storage = ConversationHistoryStorage(cls.redis_client, conversation_timeout=10)
            
            # 测试数据
            cls.user_id = f"test_user_{int(time.time())}"  # 使用时间戳确保唯一性
            cls.group_id = f"test_group_{int(time.time())}"
            cls.conversation_data = json.dumps({
                "conversation_id": str(uuid.uuid4()),
                "history": [{"user_query": "测试查询", "response": "测试回复", "timestamp": time.time()}],
                "intent": {"theme_name": "天气", "class_name": "查询"}
            }, ensure_ascii=False)
            
            logger.info(f"测试数据初始化完成: user_id={cls.user_id}, group_id={cls.group_id}")
            
            # 测试前清理可能存在的测试数据
            cls._cleanup_test_data(cls)
            
        except redis.exceptions.ConnectionError as e:
            logger.error(f"无法连接到Redis服务器: {e}")
            raise unittest.SkipTest(f"无法连接到Redis服务器: {redis_params['host']}:{redis_params['port']}")
    
    @classmethod
    def tearDownClass(cls):
        """在所有测试结束后清理测试环境"""
        if hasattr(cls, 'redis_client'):
            cls._cleanup_test_data(cls)
            logger.info("测试完成，已清理测试数据")
    
    def _cleanup_test_data(self):
        """清理测试数据"""
        # 删除测试用户的对话数据
        self.storage.delete_conversation_data(self.user_id, self.group_id)
        
        # 额外检查并删除可能的测试键
        for key in self.redis_client.keys(f"*{self.user_id}*"):
            self.redis_client.delete(key)
            logger.info(f"删除测试键: {key}")
        for key in self.redis_client.keys(f"*{self.group_id}*"):
            self.redis_client.delete(key)
            logger.info(f"删除测试键: {key}")
        
        logger.info(f"已清理用户 {self.user_id} 的测试数据")
    
    def test_conversation_key_generation(self):
        """测试对话键生成方法"""
        logger.info("开始测试: 生成对话历史的Redis键")
        
        # 测试对话历史键
        expected_key = f"conversation:{self.group_id}:{self.user_id}"
        actual_key = self.storage._get_conversation_key(self.user_id, self.group_id)
        self.assertEqual(expected_key, actual_key)
        logger.info(f"测试通过: 生成的对话历史键为 {actual_key}")
        
        # 测试对话时间键
        expected_time_key = f"conversation_time:{self.group_id}:{self.user_id}"
        actual_time_key = self.storage._get_conversation_time_key(self.user_id, self.group_id)
        self.assertEqual(expected_time_key, actual_time_key)
        logger.info(f"测试通过: 生成的对话时间键为 {actual_time_key}")
    
    def test_save_and_get_raw_conversation_data(self):
        """测试保存和获取原始对话数据"""
        logger.info("开始测试: 保存和获取原始对话数据")
        
        # 测试保存对话数据的耗时
        start_time = time.time()
        self.storage.save_raw_conversation_data(self.user_id, self.group_id, self.conversation_data)
        save_duration = time.time() - start_time
        logger.info(f"测试通过: 保存原始对话数据成功, 耗时: {save_duration:.3f}秒, 内容为 {self.conversation_data}")
        
        # 测试获取对话数据的耗时
        start_time = time.time()
        conversation_data_str, last_time = self.storage.get_raw_conversation_data(self.user_id, self.group_id)
        get_duration = time.time() - start_time
        logger.info(f"测试通过: 获取原始对话数据成功，耗时: {get_duration:.3f}秒，内容为 {conversation_data_str.decode('utf-8')}")
        
        # 验证结果
        self.assertEqual(self.conversation_data, conversation_data_str.decode('utf-8'))
        self.assertIsNotNone(last_time)
        logger.info(f"测试通过: 获取对话数据时间成功，内容为 {last_time}")
        self.assertIsInstance(last_time, float)
        logger.info("测试通过: 成功保存和获取原始对话数据")
    
    def test_delete_conversation_data(self):
        """测试删除对话数据"""
        logger.info("开始测试: 删除对话数据")
        
        # 先保存对话数据
        logger.info(f"测试中: 保存对话数据, 测试数据: {self.conversation_data}")
        self.storage.save_raw_conversation_data(self.user_id, self.group_id, self.conversation_data)
        logger.info(f"测试通过: 保存对话数据成功, 测试数据: {self.conversation_data}")
        
        # 验证数据已保存
        key = self.storage._get_conversation_key(self.user_id, self.group_id)
        time_key = self.storage._get_conversation_time_key(self.user_id, self.group_id)
        logger.info(f"测试中: 验证数据已保存, 键: {key}, 时间键: {time_key}")
        self.assertTrue(self.redis_client.exists(key))
        self.assertTrue(self.redis_client.exists(time_key))
        
        # 测试删除对话数据的耗时
        logger.info("测试中: 删除对话数据")
        start_time = time.time()
        self.storage.delete_conversation_data(self.user_id, self.group_id)
        delete_duration = time.time() - start_time
        logger.info(f"测试中: 删除对话数据完成, 耗时: {delete_duration:.3f}秒")
        
        # 验证数据已删除
        self.assertFalse(self.redis_client.exists(key))
        self.assertFalse(self.redis_client.exists(time_key))
        logger.info(f"测试中: 验证数据已删除, 键: {key}, 时间键: {time_key}")
        logger.info(f"测试通过: 成功删除对话数据")
    
    def test_check_key_exists(self):
        """测试检查键是否存在"""
        logger.info("开始测试: 检查键是否存在")
        
        # 不存在的情况
        
        temp_user_id = f"nonexistent_user_{int(time.time())}"
        self.assertFalse(self.storage.check_key_exists(temp_user_id, self.group_id))
        key = self.storage._get_conversation_key(self.user_id, self.group_id)
        logger.info(f"测试中: 键不存在, 键: {key}")
        # 存在的情况
        self.storage.save_raw_conversation_data(self.user_id, self.group_id, self.conversation_data)
        key = self.storage._get_conversation_key(self.user_id, self.group_id)
        time_key = self.storage._get_conversation_time_key(self.user_id, self.group_id)
        logger.info(f"测试中: 键存在, 键: {key}")

        self.assertTrue(self.storage.check_key_exists(self.user_id, self.group_id))
        
        logger.info("测试通过: 正确检查键是否存在")


if __name__ == "__main__":
    # 运行测试
    logger.info("开始运行ConversationHistoryStorage测试")
    unittest.main()