"""意图识别演示程序"""
import sys
import os
import json
import asyncio
import redis
from loguru import logger

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.intent_recognize_chatmodel_service import ChatModelIntentRecognizer
from services.intent_recognize_chatflow_service import ChatFlowIntentRecognizer
from config.redis_config import get_redis_connection_params
from config.chat_config import CONVERSATION_TIMEOUT
from config.intent_config import INTENT_MODEL_CONFIG

# ====== 新增：根据配置动态选择意图识别器 ======
redis_params = get_redis_connection_params()
redis_client = redis.StrictRedis(**redis_params)

if INTENT_MODEL_CONFIG["llm_service"] == "chatmodel":
    IntentRecognizerClass = ChatModelIntentRecognizer
else:
    IntentRecognizerClass = ChatFlowIntentRecognizer

intent_recognizer = IntentRecognizerClass(redis_client)


async def demo_single_query():
    """单次查询演示"""
    print("\n===== 单次查询意图识别演示 =====")
    
    # 测试参数
    user_id = "test_user_1"
    group_id = "test_group_1"
    
    # 测试不同类型的查询
    test_queries = [
        "帮我搜索最新的AI技术发展趋势",  # 期望: bing
        "公司的年度报告在哪里可以找到？",  # 期望: RAG
        "解释一下量子计算的基本原理",  # 期望: LLM
        "你今天心情怎么样？",  # 期望: other
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        intent = await intent_recognizer.recognize_intent(user_id, group_id, query)
        print(f"识别意图: {json.dumps(intent, ensure_ascii=False)}")

async def demo_multi_turn_conversation():
    """多轮对话演示"""
    print("\n===== 多轮对话意图识别演示 =====")
    
    # 测试参数
    user_id = "test_user_" + str(int(asyncio.get_event_loop().time()))
    group_id = "test_group_" + str(int(asyncio.get_event_loop().time()))
    
    # 测试多轮对话
    queries = [
        "什么是机器学习？",  # 期望: LLM
        "给我找一些相关的学习资源",  # 期望: bing
        "我们公司有哪些机器学习相关的项目？",  # 期望: RAG
        "谢谢你的帮助"  # 期望: other
    ]
    
    print(f"用户ID: {user_id}")
    print(f"组织ID: {group_id}")
    
    for i, query in enumerate(queries):
        print(f"\n轮次 {i+1}:")
        print(f"查询: {query}")
        
        intent = await intent_recognizer.recognize_intent(user_id, group_id, query)
        print(f"识别意图: {json.dumps(intent, ensure_ascii=False)}")


async def demo_intent_validation():
    """意图验证演示"""
    print("\n===== 意图验证演示 =====")
    
    # 测试参数
    user_id = "test_validation_" + str(int(asyncio.get_event_loop().time()))
    group_id = "test_group_" + str(int(asyncio.get_event_loop().time()))
    
    # 测试各种边界情况
    test_cases = [
        ("空内容查询", " "),  # 应返回 other
        ("不完整查询", "那个...呃..."),  # 应返回 other
        ("模糊查询", "帮我看看"),  # 应返回 other
        ("混合意图查询", "帮我找资料并解释一下量子计算"),  # 可能返回 bing 或 LLM
        ("无效格式查询", "!!!@@@###"),  # 应返回 other
    ]
    
    print(f"用户ID: {user_id}")
    print(f"组织ID: {group_id}")
    
    for desc, query in test_cases:
        print(f"\n测试: {desc}")
        print(f"查询: {query}")
        
        intent = await intent_recognizer.recognize_intent(user_id, group_id, query)
        print(f"识别意图: {json.dumps(intent, ensure_ascii=False)}")


async def demo_conversation_timeout():
    """对话超时演示"""
    print("\n===== 对话超时演示 =====")
    print(f"对话超时时间: {CONVERSATION_TIMEOUT}秒")
    print("注意: 此演示将等待对话超时，可能需要一些时间")
    print("为了演示效果，我们将超时时间临时设置为5秒")
    
    intent_recognizer.history_storage.conversation_timeout = 5  # 设置为5秒
    intent_recognizer.conversation_timeout = 5  # 设置为5秒
    
    # 测试参数
    user_id = "test_timeout_" + str(int(asyncio.get_event_loop().time()))
    group_id = "test_group_" + str(int(asyncio.get_event_loop().time()))
    
    # 第一轮对话
    query1 = "什么是人工智能？"  # 期望: LLM
    print(f"\n第一轮对话:")
    print(f"查询: {query1}")
    
    intent1 = await intent_recognizer.recognize_intent(user_id, group_id, query1)
    print(f"识别意图: {json.dumps(intent1, ensure_ascii=False)}")
    
    # 等待超时
    print("\n等待对话超时...")
    await asyncio.sleep(6)  # 等待超过超时时间
    
    # 第二轮对话（超时后）
    query2 = "给我找一些相关资料"  # 期望: bing (新对话)
    print(f"\n第二轮对话 (超时后):")
    print(f"查询: {query2}")
    
    intent2 = await intent_recognizer.recognize_intent(user_id, group_id, query2)
    print(f"识别意图: {json.dumps(intent2, ensure_ascii=False)}")
    print("注意: 由于对话已超时，这应该被视为新对话，而不是上下文相关的查询")


async def main():
    """主函数"""
    print("===== 意图识别演示程序 =====")
    
    # 运行各个演示
    await demo_single_query()
    await demo_multi_turn_conversation()
    await demo_intent_validation()
    await demo_conversation_timeout()
    
    print("\n===== 演示结束 =====")

if __name__ == "__main__":
    asyncio.run(main())