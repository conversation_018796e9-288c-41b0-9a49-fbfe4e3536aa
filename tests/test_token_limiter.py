#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符数限制器测试文件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
 # 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")
        
import pytest
from utils.token_limiter import CharacterLimiter


class TestCharacterLimiter:
    """字符数限制器测试类"""

    def setup_method(self):
        """测试前准备"""
        self.limiter = CharacterLimiter(max_chars=1000)  # 使用较小的限制便于测试
    
    def test_count_chars(self):
        """测试字符数计算功能"""
        # 测试空字符串
        assert self.limiter.count_chars("") == 0

        # 测试中文字符
        chinese_text = "这是一个测试"
        char_count = self.limiter.count_chars(chinese_text)
        assert char_count == len(chinese_text)

        # 测试英文字符
        english_text = "This is a test"
        char_count = self.limiter.count_chars(english_text)
        assert char_count == len(english_text)

        # 测试混合文本
        mixed_text = "这是test测试"
        char_count = self.limiter.count_chars(mixed_text)
        assert char_count == len(mixed_text)
    
    def test_truncate_text(self):
        """测试文本截断功能"""
        # 测试短文本不截断
        short_text = "短文本"
        result = self.limiter.truncate_text(short_text, 100)
        assert result == short_text

        # 测试长文本截断
        long_text = "这是一个很长的文本" * 100  # 创建长文本
        result = self.limiter.truncate_text(long_text, 50)
        assert len(result) <= 50
        assert len(result) == 50
    
    def test_limit_history(self):
        """测试历史消息限制功能"""
        # 创建测试历史消息
        history = []
        for i in range(10):
            history.append({
                "query": f"这是第{i}个查询" * 10,
                "content": f"这是第{i}个回答" * 10
            })

        # 测试限制
        limited_history = self.limiter.limit_history(history, 500)

        # 验证结果
        assert len(limited_history) <= len(history)

        # 计算总字符数
        total_chars = 0
        for item in limited_history:
            total_chars += len(item["query"])
            total_chars += len(item["content"])
        assert total_chars <= 500
    
    def test_limit_knowledge(self):
        """测试知识内容限制功能"""
        # 创建长知识内容
        long_knowledge = "这是知识内容。\n\n" * 200

        # 测试限制
        limited_knowledge = self.limiter.limit_knowledge(long_knowledge, 100)

        # 验证结果
        assert len(limited_knowledge) <= len(long_knowledge)
        assert len(limited_knowledge) <= 100
    
    def test_limit_query(self):
        """测试查询限制功能"""
        # 创建长查询
        long_query = "这是一个很长的查询" * 50

        # 测试限制
        limited_query = self.limiter.limit_query(long_query, 50)

        # 验证结果
        assert len(limited_query) <= len(long_query)
        assert len(limited_query) <= 50
    
    def test_limit_messages_for_rag_qa(self):
        """测试RAG问答的消息限制功能"""
        # 创建测试数据 - 总字符数超过限制
        query = "这是一个查询" * 20  # 140字符
        history = [
            {"query": "历史查询1" * 10, "content": "历史回答1" * 10},  # 100字符
            {"query": "历史查询2" * 10, "content": "历史回答2" * 10},  # 100字符
        ]
        knowledge = "这是知识内容" * 50  # 300字符
        # 总计约540字符，超过500的限制

        # 测试限制
        limited_query, limited_history, limited_knowledge = self.limiter.limit_messages_for_rag_qa(
            query, history, knowledge, max_total_chars=500
        )

        # 验证结果
        assert len(limited_query) <= len(query)
        assert len(limited_history) <= len(history)
        assert len(limited_knowledge) <= len(knowledge)

        # 验证总字符数不超过限制
        total_chars = len(limited_query)
        for item in limited_history:
            total_chars += len(item.get("query", ""))
            total_chars += len(item.get("content", ""))
        total_chars += len(limited_knowledge)

        # 考虑系统prompt的预留空间，总数应该合理
        assert total_chars <= 500

        # 测试未超过限制的情况
        short_query = "短查询"
        short_history = [{"query": "短历史", "content": "短回答"}]
        short_knowledge = "短知识"

        limited_query2, limited_history2, limited_knowledge2 = self.limiter.limit_messages_for_rag_qa(
            short_query, short_history, short_knowledge, max_total_chars=500
        )

        # 应该返回原内容
        assert limited_query2 == short_query
        assert limited_history2 == short_history
        assert limited_knowledge2 == short_knowledge
    
    def test_limit_messages_for_llm_qa(self):
        """测试LLM问答的消息限制功能"""
        # 创建测试数据 - 总字符数超过限制
        query = "这是一个查询" * 20  # 140字符
        history = [
            {"query": "历史查询1" * 10, "content": "历史回答1" * 10},  # 100字符
        ]
        web_context = "这是联网搜索上下文" * 30  # 330字符
        # 总计约570字符，超过500的限制

        # 测试有联网搜索的情况
        limited_query, limited_history, limited_web_context = self.limiter.limit_messages_for_llm_qa(
            query, history, web_context, max_total_chars=500
        )

        # 验证结果
        assert len(limited_query) <= len(query)
        assert len(limited_history) <= len(history)
        assert len(limited_web_context) <= len(web_context)

        # 测试无联网搜索的情况
        limited_query2, limited_history2, limited_web_context2 = self.limiter.limit_messages_for_llm_qa(
            query, history, "", max_total_chars=500
        )

        # 验证结果
        assert limited_web_context2 == ""
        assert len(limited_query2) <= len(query)
        assert len(limited_history2) <= len(history)

        # 测试未超过限制的情况
        short_query = "短查询"
        short_history = [{"query": "短历史", "content": "短回答"}]
        short_web_context = "短搜索"

        limited_query3, limited_history3, limited_web_context3 = self.limiter.limit_messages_for_llm_qa(
            short_query, short_history, short_web_context, max_total_chars=500
        )

        # 应该返回原内容
        assert limited_query3 == short_query
        assert limited_history3 == short_history
        assert limited_web_context3 == short_web_context
    
    def test_limit_messages_for_doc_qa(self):
        """测试文档问答的消息限制功能"""
        # 创建测试数据 - 总字符数超过限制
        query = "这是一个查询" * 20  # 140字符
        history = [
            {"query": "历史查询1" * 10, "content": "历史回答1" * 10},  # 100字符
        ]
        document = "这是文档内容" * 50  # 300字符
        # 总计约540字符，超过500的限制

        # 测试限制
        limited_query, limited_history, limited_document = self.limiter.limit_messages_for_doc_qa(
            query, history, document, max_total_chars=500
        )

        # 验证结果
        assert len(limited_query) <= len(query)
        assert len(limited_history) <= len(history)
        assert len(limited_document) <= len(document)

        # 验证总字符数不超过限制
        total_chars = len(limited_query)
        for item in limited_history:
            total_chars += len(item.get("query", ""))
            total_chars += len(item.get("content", ""))
        total_chars += len(limited_document)

        # 考虑系统prompt的预留空间，总数应该合理
        assert total_chars <= 500

        # 测试未超过限制的情况
        short_query = "短查询"
        short_history = [{"query": "短历史", "content": "短回答"}]
        short_document = "短文档"

        limited_query2, limited_history2, limited_document2 = self.limiter.limit_messages_for_doc_qa(
            short_query, short_history, short_document, max_total_chars=500
        )

        # 应该返回原内容
        assert limited_query2 == short_query
        assert limited_history2 == short_history
        assert limited_document2 == short_document
    
    def test_calculate_total_chars(self):
        """测试消息列表总字符数计算功能"""
        messages = [
            {"role": "system", "content": "系统提示"},
            {"role": "user", "content": "用户查询"},
            {"role": "assistant", "content": "助手回答"}
        ]

        total_chars = self.limiter.calculate_total_chars(messages)
        expected_chars = sum(len(msg["content"]) for msg in messages)
        assert total_chars == expected_chars
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试空输入
        assert self.limiter.limit_query("", 100) == ""
        assert self.limiter.limit_history([], 100) == []
        assert self.limiter.limit_knowledge("", 100) == ""

        # 测试None输入
        assert self.limiter.limit_query(None, 100) is None
        assert self.limiter.limit_history(None, 100) is None
        assert self.limiter.limit_knowledge(None, 100) is None

        # 测试无效历史记录格式
        invalid_history = [
            {"invalid": "data"},
            {"query": "valid query", "content": "valid content"},
            "invalid_item"
        ]
        limited = self.limiter.limit_history(invalid_history, 100)
        # 应该只保留有效的历史记录
        assert len(limited) == 1
        assert limited[0]["query"] == "valid query"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
