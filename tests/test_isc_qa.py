"""
ISCQA测试模块

该模块包含对ISCQA类的各种测试用例，包括：
- 单轮对话测试
- 多轮对话测试
- 流式输出测试
- 不同模式测试（strict/common）

运行方式：
1. 单独运行某个测试：python -m pytest tests/test_isc_qa.py::test_iscqa_single_turn -v
2. 运行所有测试：python -m pytest tests/test_isc_qa.py -v
3. 直接运行脚本：python tests/test_isc_qa.py
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipelines.isc_qa import ISCQA
import pytest

@pytest.mark.asyncio
async def test_iscqa_single_turn():
    """测试ISCQA单轮对话"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_single")
    query = "什么是信息安全合规？"
    user_id = "test_user_single"
    history = []
    result = await iscqa.generate(query, user_id, history)
    print("单轮对话结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_iscqa_multi_turn():
    """测试ISCQA多轮对话"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_multi")
    user_id = "test_user_multi"
    history = [
        {"query": "什么是ISO 27001？", "content": "ISO 27001是信息安全管理体系标准..."},
        {"query": "ISO 27001的主要要求", "content": "主要要求包括..."}
    ]
    query = "如何实施ISO 27001？"
    result = await iscqa.generate(query, user_id, history)
    print("多轮对话结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_iscqa_stream_single_turn():
    """测试ISCQA单轮对话流式输出"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_stream_single")
    query = "什么是信息安全合规？"
    user_id = "test_user_stream_single"
    history = []
    chunks = []
    async for chunk in iscqa.generate_stream(query, user_id, history):
        print("流式单轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "content" in chunk or "choices" in chunk

@pytest.mark.asyncio
async def test_iscqa_stream_multi_turn():
    """测试ISCQA多轮对话流式输出"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_stream_multi")
    user_id = "test_user_stream_multi"
    history = [
        {"query": "什么是ISO 27001？", "content": "ISO 27001是信息安全管理体系标准..."},
        {"query": "ISO 27001的主要要求", "content": "主要要求包括..."}
    ]
    query = "如何实施ISO 27001？"
    chunks = []
    async for chunk in iscqa.generate_stream(query, user_id, history):
        print("流式多轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "content" in chunk or "choices" in chunk

@pytest.mark.asyncio
async def test_iscqa_strict_mode():
    """测试ISCQA严格模式"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_strict")
    query = "什么是信息安全合规？"
    user_id = "test_user_strict"
    history = []
    result = await iscqa.generate(query, user_id, history, mode="strict")
    print("严格模式结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_iscqa_common_mode():
    """测试ISCQA普通模式"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_common")
    query = "什么是信息安全合规？"
    user_id = "test_user_common"
    history = []
    result = await iscqa.generate(query, user_id, history, mode="common")
    print("普通模式结果:", result)
    assert "content" in result or "choices" in result

@pytest.mark.asyncio
async def test_iscqa_with_parameters():
    """测试ISCQA带参数调用"""
    iscqa = ISCQA(model_id="qwen3_32b", request_id="req_params")
    query = "什么是信息安全合规？"
    user_id = "test_user_params"
    history = []
    result = await iscqa.generate(
        query, 
        user_id, 
        history, 
        top_k=20, 
        top_r=10, 
        min_score=0.6,
        temperature=0.7,
        top_p=0.9
    )
    print("带参数结果:", result)
    assert "content" in result or "choices" in result

if __name__ == "__main__":
    # asyncio.run(test_iscqa_single_turn())
    # asyncio.run(test_iscqa_multi_turn())
    asyncio.run(test_iscqa_stream_single_turn())
    # asyncio.run(test_iscqa_stream_multi_turn())
    # asyncio.run(test_iscqa_strict_mode())
    # asyncio.run(test_iscqa_common_mode())
    # asyncio.run(test_iscqa_with_parameters())
