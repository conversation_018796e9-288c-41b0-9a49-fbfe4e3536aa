#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例：展示如何使用LLM提供者架构

这个示例展示了：
1. 如何区分和使用chatflow服务和model服务
2. 如何根据服务类型调用不同的方法
3. 如何处理不同类型服务的响应
4. 架构验证测试
"""

import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")


from core.llm_provider import (
    get_llm_provider, 
    get_chatflow_providers, 
    get_model_providers,
    is_chatflow_service,
    is_model_service
)
from config.model_config import get_service_info, get_chatflow_services, get_model_services

def test_architecture():
    """架构验证测试"""
    print("=== 架构验证测试 ===")
    
    # 测试服务分类
    print("\n1. 服务分类测试:")
    chatflow_providers = get_chatflow_providers()
    model_providers = get_model_providers()
    
    print(f"Chatflow服务: {list(chatflow_providers.keys())}")
    print(f"Model服务: {list(model_providers.keys())}")
    
    # 测试服务信息
    print("\n2. 服务信息测试:")
    for model_id in ['gpt_4o', 'qwen3_32b', 'qwen3_8b']:
        try:
            info = get_service_info(model_id)
            print(f"{model_id}: {info['service_type']} - {info['description']}")
            print(f"  特性: {info['features']}")
        except Exception as e:
            print(f"{model_id}: 错误 - {e}")
    
    # 测试提供者创建
    print("\n3. 提供者创建测试:")
    for model_id in ['gpt_4o', 'qwen3_32b', 'qwen3_8b']:
        try:
            provider = get_llm_provider(model_id)
            print(f"{model_id}: {provider.__class__.__name__} (服务类型: {provider.service_type})")
        except Exception as e:
            print(f"{model_id}: 错误 - {e}")
    
    # 测试服务类型判断
    print("\n4. 服务类型判断测试:")
    for model_id in ['gpt_4o', 'qwen3_32b', 'qwen3_8b']:
        is_chatflow = is_chatflow_service(model_id)
        is_model = is_model_service(model_id)
        print(f"{model_id}: chatflow={is_chatflow}, model={is_model}")
    
    print("\n=== 测试完成 ===")

def demonstrate_service_types():
    """演示服务类型的区分"""
    print("=== 服务类型演示 ===")
    
    # 获取所有chatflow服务
    chatflow_services = get_chatflow_services()
    print(f"Chatflow服务: {chatflow_services}")
    
    # 获取所有model服务
    model_services = get_model_services()
    print(f"Model服务: {model_services}")
    
    # 获取服务详细信息
    for model_id in chatflow_services + model_services:
        info = get_service_info(model_id)
        print(f"\n{model_id}:")
        print(f"  类型: {info['service_type']}")
        print(f"  描述: {info['description']}")
        print(f"  特性: {info['features']}")

async def demonstrate_chatflow_usage():
    """演示chatflow服务的使用"""
    print("\n=== Chatflow服务使用演示 ===")
    
    # 使用意图识别chatflow服务
    try:
        provider = get_llm_provider("gpt_4o", request_id="demo-001")
        print(f"创建了 {provider.__class__.__name__} 实例")
        print(f"服务类型: {provider.service_type}")
        
        # Chatflow服务支持连续对话
        response = await provider.generate(
            inputs={"system_prompt": "你是一个智能助手，专门用于意图识别。"},
            query="我想订一张明天去北京的机票，输出json格式",
            conversation_id="",
            user_id="user-456"
        )
        print(f"响应: {response}")
        
    except Exception as e:
        print(f"Chatflow服务调用示例（需要实际API）: {e}")

async def demonstrate_model_usage():
    """演示model服务的使用"""
    print("\n=== Model服务使用演示 ===")
    
    # 使用纯模型服务
    try:
        provider = get_llm_provider("qwen3_32b", request_id="demo-002")
        print(f"创建了 {provider.__class__.__name__} 实例")
        print(f"服务类型: {provider.service_type}")
        messages = [
            {"role": "system", "content": "你是一个智能助手，专门用于意图识别。"},
            {"role": "user", "content": "我想订一张明天去北京的机票，输出json格式"}
        ]
        # Model服务直接调用模型
        response = await provider.generate(
            messages,
            temperature=0.8,
            max_tokens=256
        )
        print(f"响应: {response}")
        
    except Exception as e:
        print(f"Model服务调用示例（需要实际API）: {e}")

def demonstrate_dynamic_usage():
    """演示动态选择服务类型"""
    print("\n=== 动态服务选择演示 ===")
    
    model_ids = ["gpt_4o", "qwen3_32b", "qwen3_8b"]
    
    for model_id in model_ids:
        print(f"\n处理模型: {model_id}")
        
        # 动态判断服务类型
        if is_chatflow_service(model_id):
            print(f"  -> 这是一个chatflow服务")
            provider = get_llm_provider(model_id)
            # 可以传入conversation_id等chatflow特有参数
            print(f"  -> 支持连续对话和工作流")
            
        elif is_model_service(model_id):
            print(f"  -> 这是一个model服务")
            provider = get_llm_provider(model_id)
            # 可以传入temperature等模型特有参数
            print(f"  -> 支持直接模型调用")
            
        else:
            print(f"  -> 未知服务类型")

def demonstrate_provider_mapping():
    """演示提供者映射"""
    print("\n=== 提供者映射演示 ===")
    
    # 获取chatflow提供者
    chatflow_providers = get_chatflow_providers()
    print(f"Chatflow提供者: {list(chatflow_providers.keys())}")
    
    # 获取model提供者
    model_providers = get_model_providers()
    print(f"Model提供者: {list(model_providers.keys())}")
    
    # 展示每个提供者的类型
    for model_id, provider_class in {**chatflow_providers, **model_providers}.items():
        print(f"  {model_id}: {provider_class.__name__}")

async def main():
    """主函数"""
    print("LLM提供者架构使用示例与验证测试")
    print("=" * 50)
    
    # 运行架构验证测试
    test_architecture()
    
    # 运行各种演示
    demonstrate_service_types()
    await demonstrate_chatflow_usage()
    await demonstrate_model_usage()
    demonstrate_dynamic_usage()
    demonstrate_provider_mapping()

if __name__ == "__main__":
    asyncio.run(main())