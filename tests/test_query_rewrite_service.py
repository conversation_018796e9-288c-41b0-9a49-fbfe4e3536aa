#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

import pytest
from services.query_rewrite_service import QueryRewriteService


class TestQueryRewriteService:
    """QueryRewriteService 测试类"""
    
    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.service = QueryRewriteService(request_id="test_request")
    
    def test_init(self):
        """测试服务初始化"""
        assert self.service is not None
        assert hasattr(self.service, 'logger')
        assert hasattr(self.service, 'CODE_PATTERN')
        assert hasattr(self.service, 'STANDALONE_CODE_PATTERN')
        assert isinstance(self.service.STOPWORDS, set)
        assert len(self.service.STOPWORDS) > 0
    
    def test_extract_and_process_codes(self):
        """测试独立代码提取"""
        # 测试包含字母数字混合代码的文本
        text1 = "SEW F43"
        codes1 = self.service.extract_and_process_codes(text1)
        assert "CODE_F43" in codes1
        
        # 测试包含多个代码的文本
        text2 = "MC07B F111 和 ABC123"
        codes2 = self.service.extract_and_process_codes(text2)
        assert "CODE_MC07B" in codes2
        assert "CODE_F111" in codes2
        assert "CODE_ABC123" in codes2
        
        # 测试不包含代码的文本
        text3 = "这是一个普通的文本"
        codes3 = self.service.extract_and_process_codes(text3)
        assert len(codes3) == 0
    
    def test_normalize_code_terms(self):
        """测试带前缀代码标准化"""
        # 测试错误代码
        text1 = "错误代码123"
        codes1 = self.service.normalize_code_terms(text1)
        assert len(codes1) > 0
        
        # 测试故障代码
        text2 = "故障代码 F43"
        codes2 = self.service.normalize_code_terms(text2)
        assert len(codes2) > 0
        
        # 测试不包含代码前缀的文本
        text3 = "普通文本内容"
        codes3 = self.service.normalize_code_terms(text3)
        assert len(codes3) == 0
    
    def test_custom_tokenize(self):
        """测试自定义分词"""
        text = "SEW F43 错误代码"
        segments, codes = self.service.custom_tokenize(text)
        
        # 检查返回值类型
        assert isinstance(segments, list)
        assert isinstance(codes, list)
        
        # 检查是否提取到代码
        assert len(codes) > 0
        
        # 检查分词结果
        assert len(segments) > 0
    
    def test_generate_number_variants(self):
        """测试数字变体生成"""
        # 测试单位数字
        variants1 = self.service.generate_number_variants("F7")
        assert "7" in variants1
        assert "07" in variants1
        assert "007" in variants1
        
        # 测试双位数字
        variants2 = self.service.generate_number_variants("F43")
        assert "43" in variants2
        assert "043" in variants2
        
        # 测试三位数字
        variants3 = self.service.generate_number_variants("F111")
        assert "111" in variants3
        # 三位数字不应该再补零
        assert len(variants3) == 1
        
        # 测试不包含数字的代码
        variants4 = self.service.generate_number_variants("ABC")
        assert variants4 == ["ABC"]
    
    def test_replace_codes_in_text(self):
        """测试文本中代码替换"""
        # 测试F开头的代码替换
        text = "SEW F43"
        codes = ["CODE_F43"]
        variants = self.service.replace_codes_in_text(text, codes)
        
        # 应该生成多个变体
        assert len(variants) > 1
        assert "SEW 43" in variants
        assert "SEW 043" in variants
        
        # 测试不符合替换条件的代码
        text2 = "MC07B 设备"
        codes2 = ["CODE_MC07B"]
        variants2 = self.service.replace_codes_in_text(text2, codes2)
        # MC07B不以F开头，不应该被替换
        assert len(variants2) == 0
    
    def test_rewrite_query_success(self):
        """测试查询改写成功案例"""
        query = "SEW F43 是什么报警？"
        result = self.service.rewrite_query(query)
        
        # 检查返回结果结构
        assert "original_query" in result
        assert "extracted_codes" in result
        assert "tokenized_segments" in result
        assert "query_variants" in result
        assert "variant_count" in result
        
        # 检查原始查询
        assert result["original_query"] == query
        
        # 检查是否提取到代码
        assert len(result["extracted_codes"]) > 0
        
        # 检查是否生成了变体
        assert result["variant_count"] > 0
        assert len(result["query_variants"]) > 0
    
    def test_rewrite_query_no_codes(self):
        """测试不包含代码的查询改写"""
        query = "这是一个普通的问题"
        result = self.service.rewrite_query(query)
        
        # 应该返回空的变体列表
        assert result["variant_count"] == 0
        assert len(result["query_variants"]) == 0
        assert result["original_query"] == query
    
    def test_batch_rewrite_queries(self):
        """测试批量查询改写"""
        queries = [
            "SEW F43",
            "MC07B F111",
            "错误代码F7是什么？",
            "普通问题"
        ]
        
        results = self.service.batch_rewrite_queries(queries)
        
        # 检查返回结果数量
        assert len(results) == len(queries)
        
        # 检查每个结果的结构
        for result in results:
            assert "original_query" in result
            assert "extracted_codes" in result
            assert "query_variants" in result
            assert "variant_count" in result
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空字符串
        result1 = self.service.rewrite_query("")
        assert result1["original_query"] == ""
        assert result1["variant_count"] == 0
        
        # 只有空格
        result2 = self.service.rewrite_query("   ")
        assert result2["variant_count"] == 0
        
        # 特殊字符
        result3 = self.service.rewrite_query("!@#$%^&*()")
        assert result3["variant_count"] == 0


def test_query_rewrite_integration():
    """集成测试：完整的查询改写流程"""
    service = QueryRewriteService(request_id="integration_test")
    
    # 测试复杂查询
    complex_query = "EMS小车控制器报警代码F008是什么？"
    result = service.rewrite_query(complex_query)
    
    print(f"\n集成测试结果:")
    print(f"原始查询: {result['original_query']}")
    print(f"提取的代码: {result['extracted_codes']}")
    print(f"分词结果: {result['tokenized_segments']}")
    print(f"查询变体数量: {result['variant_count']}")
    print(f"查询变体:")
    for i, variant in enumerate(result['query_variants'], 1):
        print(f"  变体{i}: {variant}")
    
    assert result['variant_count'] > 0
    assert len(result['query_variants']) > 0


if __name__ == "__main__":
    # 运行特定测试
    print("运行QueryRewriteService测试...")
    
    # 运行集成测试
    test_query_rewrite_integration()
    
    # 运行所有测试
    pytest.main([__file__, "-v"])
