#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
chatmodel_service测试用例
"""
import sys
import os
import asyncio
import unittest
import time
import json
import uuid
import redis
from loguru import logger

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from config.logging_config import configure_logging
from config.redis_config import get_redis_connection_params
from config.chat_config import CONVERSATION_TIMEOUT, MAX_CONVERSATION_ROUNDS
from core.llm_provider import get_llm_provider
from services.chatmodel_service import ModelService, ChatService
from services.chat_storage_service import ConversationHistoryStorage

# 配置日志
configure_logging()

class TestConversationManager(unittest.IsolatedAsyncioTestCase):
    """ConversationManager测试用例"""
    
    @classmethod
    def setUpClass(cls):
        """在所有测试开始前设置测试环境"""
        # 获取Redis连接参数
        redis_params = get_redis_connection_params()
        try:
            # 创建Redis客户端
            cls.redis_client = redis.StrictRedis(**redis_params)
            # 测试连接
            cls.redis_client.ping()
            logger.info("成功连接到Redis服务器")
            
            # 初始化LLMProvider (使用mock对象替代真实LLM)
            cls.llm_provider = get_llm_provider("qwen3_32b")
            cls.conversation_session = ModelService(cls.llm_provider)
            
            # 初始化对话历史存储
            cls.history_storage = ConversationHistoryStorage(cls.redis_client, conversation_timeout=10)  # 10秒超时，方便测试
            
            # 初始化对话管理类
            cls.conversation_manager = ChatService(cls.conversation_session, cls.history_storage, max_conversation_rounds = MAX_CONVERSATION_ROUNDS)
            
            # 测试数据
            cls.user_id = f"test_user_{int(time.time())}"  # 使用时间戳确保唯一性
            cls.group_id = f"test_group_{int(time.time())}"
            cls.conversation_id = str(uuid.uuid4())
            cls.user_query = "测试查询"
            cls.response = "测试回复"
            cls.intent = {"theme_name": "天气", "class_name": "查询"}
            
            logger.info(f"测试数据初始化完成: user_id={cls.user_id}, group_id={cls.group_id}, conversation_id={cls.conversation_id}")
            
        except redis.exceptions.ConnectionError as e:
            logger.error(f"无法连接到Redis服务器: {e}")
            raise unittest.SkipTest("无法连接到Redis服务器")
    
    @classmethod
    def tearDownClass(cls):
        """在所有测试结束后清理测试环境"""
        if hasattr(cls, 'conversation_manager'):
            cls.conversation_manager.clear_conversation(cls.user_id, cls.group_id)
            logger.info("测试完成，已清理测试数据")
    
    def setUp(self):
        """每个测试开始前的准备工作"""
        # 清除可能存在的测试数据
        self.conversation_manager.clear_conversation(self.user_id, self.group_id)
    
    def test_is_new_conversation_scenarios(self):
        """测试各种情况下的新对话判断"""
        logger.info("开始测试: 新对话判断的各种场景")
        
        # 场景1: 无历史记录
        temp_user_id = f"new_user_{int(time.time())}"
        logger.info(f"场景1 - 无历史记录: 用户ID={temp_user_id}")
        result = self.conversation_manager.is_new_conversation(temp_user_id, self.group_id)
        self.assertTrue(result)
        logger.info("场景1测试通过: 无历史记录时正确识别为新对话")
        
        # 场景2: 对话超时
        temp_user_id = f"timeout_user_{int(time.time())}"
        key = self.history_storage._get_conversation_key(temp_user_id, self.group_id)
        time_key = self.history_storage._get_conversation_time_key(temp_user_id, self.group_id)
        
        # 保存对话数据
        conversation_data = {
            "conversation_id": str(uuid.uuid4()),
            "history": [{"user_query": "测试查询", "response": "测试回复", "timestamp": time.time()}],
            "intent": self.intent
        }
        
        # 保存到Redis
        self.redis_client.set(key, json.dumps(conversation_data))
        timeout_timestamp = time.time() - 20  # 设置为20秒前的时间戳，超过了10秒的超时时间
        self.redis_client.set(time_key, timeout_timestamp)
        
        logger.info(f"场景2 - 对话超时: 用户ID={temp_user_id}, 键={key}, 时间戳={timeout_timestamp}")
        result = self.conversation_manager.is_new_conversation(temp_user_id, self.group_id)
        self.assertTrue(result)
        
        # 验证键是否已被删除
        self.assertFalse(self.redis_client.exists(key))
        self.assertFalse(self.redis_client.exists(time_key))
        logger.info("场景2测试通过: 对话超时时正确识别为新对话并删除过期数据")
        
        # 场景3: 活跃对话
        temp_user_id = f"active_user_{int(time.time())}"
        key = self.history_storage._get_conversation_key(temp_user_id, self.group_id)
        time_key = self.history_storage._get_conversation_time_key(temp_user_id, self.group_id)
        
        # 保存对话数据
        conversation_data = {
            "conversation_id": str(uuid.uuid4()),
            "history": [{"user_query": "测试查询", "response": "测试回复", "timestamp": time.time()}],
            "intent": self.intent
        }
        
        # 保存到Redis
        self.redis_client.set(key, json.dumps(conversation_data))
        active_timestamp = time.time() - 5  # 5秒前的时间戳，未超时
        self.redis_client.set(time_key, active_timestamp)
        
        logger.info(f"场景3 - 活跃对话: 用户ID={temp_user_id}, 键={key}, 时间戳={active_timestamp}")
        result = self.conversation_manager.is_new_conversation(temp_user_id, self.group_id)
        self.assertFalse(result)
        
        # 清理测试数据
        self.redis_client.delete(key)
        self.redis_client.delete(time_key)
        logger.info("场景3测试通过: 活跃对话时正确识别为非新对话")
    
    def test_conversation_lifecycle(self):
        """测试对话的完整生命周期：创建、获取、更新、切换ID和清除"""
        logger.info("开始测试: 对话的完整生命周期")
        
        # 步骤1: 创建新对话
        logger.info("步骤1: 创建新对话")
        self.conversation_manager.save_conversation_intent(
            self.user_id, self.group_id, self.conversation_id, 
            self.user_query, self.response, self.intent
        )
        
        # 验证数据是否成功保存
        key = self.history_storage._get_conversation_key(self.user_id, self.group_id)
        time_key = self.history_storage._get_conversation_time_key(self.user_id, self.group_id)
        
        # 检查键是否存在
        self.assertTrue(self.redis_client.exists(key))
        self.assertTrue(self.redis_client.exists(time_key))
        
        # 步骤2: 获取对话历史
        logger.info("步骤2: 获取对话历史")
        conversation_id, history, intent = self.conversation_manager.get_conversation_history(self.user_id, self.group_id)
        
        # 验证结果
        self.assertEqual(self.conversation_id, conversation_id)
        self.assertEqual(1, len(history))
        self.assertEqual(self.user_query, history[0]["user_query"])
        self.assertEqual(self.response, history[0]["response"])
        self.assertEqual(self.intent, intent)
        
        logger.info(f"步骤2完成: 成功获取对话历史, 对话ID={conversation_id}, 历史记录数={len(history)}")
        
        # 步骤3: 检查是否为新对话
        logger.info("步骤3: 检查是否为新对话")
        result = self.conversation_manager.is_new_conversation(self.user_id, self.group_id)
        self.assertFalse(result)
        logger.info("步骤3完成: 正确识别为非新对话")
        
        # 步骤4: 向现有对话添加新消息
        logger.info("步骤4: 向现有对话添加新消息")
        new_query = "第二次测试查询"
        new_response = "第二次测试回复"
        
        self.conversation_manager.save_conversation_intent(
            self.user_id, self.group_id, self.conversation_id, 
            new_query, new_response, self.intent
        )
        
        # 验证数据是否成功保存
        conversation_id, history, intent = self.conversation_manager.get_conversation_history(self.user_id, self.group_id)
        
        # 验证结果
        self.assertEqual(self.conversation_id, conversation_id)
        self.assertEqual(2, len(history))  # 现在应该有两条消息
        self.assertEqual(self.user_query, history[0]["user_query"])  # 第一条消息
        self.assertEqual(new_query, history[1]["user_query"])  # 第二条消息
        self.assertEqual(new_response, history[1]["response"])
        
        logger.info(f"步骤4完成: 成功向现有对话添加新消息, 历史记录数={len(history)}, 最新消息={json.dumps(history[1], ensure_ascii=False)}")
        
        # 步骤5: 使用不同对话ID保存对话
        logger.info("步骤5: 使用不同对话ID保存对话")
        new_conversation_id = str(uuid.uuid4())
        new_query = "新对话测试查询"
        new_response = "新对话测试回复"
        
        logger.info(f"原对话ID: {self.conversation_id}, 新对话ID: {new_conversation_id}")
        
        self.conversation_manager.save_conversation_intent(
            self.user_id, self.group_id, new_conversation_id, 
            new_query, new_response, self.intent
        )
        
        # 验证数据是否成功保存
        conversation_id, history, intent = self.conversation_manager.get_conversation_history(self.user_id, self.group_id)
        
        # 验证结果
        self.assertEqual(new_conversation_id, conversation_id)  # 对话ID应该更新
        self.assertEqual(1, len(history))  # 历史应该被清空，只有一条新消息
        self.assertEqual(new_query, history[0]["user_query"])
        self.assertEqual(new_response, history[0]["response"])
        
        logger.info(f"步骤5完成: 成功使用不同对话ID保存对话, 新对话ID={conversation_id}, 历史记录数={len(history)}")
        
        # 步骤6: 清除对话
        logger.info("步骤6: 清除对话历史")
        self.conversation_manager.clear_conversation(self.user_id, self.group_id)
        
        # 验证数据是否成功清除
        key = self.history_storage._get_conversation_key(self.user_id, self.group_id)
        time_key = self.history_storage._get_conversation_time_key(self.user_id, self.group_id)
        
        # 检查键是否已被删除
        self.assertFalse(self.redis_client.exists(key))
        self.assertFalse(self.redis_client.exists(time_key))
        
        # 验证获取历史返回空结果
        conversation_id, history, intent = self.conversation_manager.get_conversation_history(self.user_id, self.group_id)
        self.assertIsNone(conversation_id)
        self.assertEqual([], history)
        self.assertIsNone(intent)
        
        logger.info(f"步骤6完成: 成功清除对话历史, 键 {key} 和 {time_key} 已被删除")
    
    def test_update_conversation_intent(self):
        """测试更新对话意图"""
        logger.info("开始测试: 更新对话意图")
        
        # 先创建对话
        self.conversation_manager.save_conversation_intent(
            self.user_id, self.group_id, self.conversation_id, 
            self.user_query, self.response, self.intent
        )
        
        # 更新意图
        new_intent = {"theme_name": "交通", "class_name": "查询"}
        self.conversation_manager.update_conversation_intent(self.user_id, self.group_id, new_intent)
        
        # 验证意图是否更新
        _, _, intent = self.conversation_manager.get_conversation_history(self.user_id, self.group_id)
        self.assertEqual(new_intent, intent)
        
        logger.info("测试通过: 成功更新对话意图")

    async def test_multi_turn_chat(self):
        """测试多轮对话功能"""
        logger.info("开始测试: 多轮对话功能")
        
        # 测试参数
        system_prompt = """分析用户输入的意图，从以下类别中选择最合适的一项：
        - 'bing',       # 联网检索问答
        - 'RAG',        # 知识库问答
        - 'LLM',        # 依靠模型自身能力问答
        - 'other'       # 其他类型问答

        返回要求：
        - JSON格式包含intent, confidence, reasoning三个字段
        - confidence为0.0-1.0的浮点数
        - reasoning用中文说明分类理由

        示例回复：
        {{
            "intent": "RAG",
            "confidence": 0.95,
            "reasoning": "用户询问了知识库中的信息"
        }}"""
        user_id = f"multi_turn_user_{int(time.time())}"
        group_id = f"multi_turn_group_{int(time.time())}"
        
        # 测试多轮对话
        user_queries = [
            "我想查下北京的天气",
            "还有上海的",
            "还有广州的",
            "还有深圳的"
        ]
        
        logger.info(f"===== 测试用例：多轮对话 ===== 用户ID: {user_id}")
        for i, user_query in enumerate(user_queries):
            # 使用await等待异步函数执行完成
            result = await self.conversation_manager.process_message_intent(system_prompt, user_query, user_id, group_id)
            
            # 验证结果包含必要字段
            self.assertIn('response', result)
            self.assertIn('conversation_id', result)
            self.assertIn('is_new_conversation', result)
            
            # 第一轮应该是新对话，后续轮次不是新对话
            if i == 0:
                self.assertTrue(result['is_new_conversation'])
            else:
                self.assertFalse(result['is_new_conversation'])
            
            print(f"第{i+1}轮对话 - 用户: {user_query}")
            print(f"第{i+1}轮对话 - 回复: {result['response']}")
            print(f"第{i+1}轮对话 - 会话ID: {result['conversation_id']}")
        
        # 清理测试数据
        self.conversation_manager.clear_conversation(user_id, group_id)
        logger.info("多轮对话测试完成，已清理测试数据")

    async def test_single_turn_chat(self):
        """测试单轮对话功能"""
        logger.info("开始测试: 单轮对话功能")
        
        # 测试参数
        system_prompt = """你是一个有用的AI助理。你的任务是帮助用户回答问题。对于每个问题，你需要基于以下准则判断问题的具体类别。
        1、如果问题涉及以下情况，请回复'LLM'.\n-常识性问题，如数学问题（例如1+1等于几）、科学事实（例如水的化学式是什么）、语言学问题（例如某个单词的意思）。\n-历史事实或普遍接受的知识，如历史事件的日期、科学理论的基本解释。\n-日常生活问题，如“我为什么没有参加父母的婚礼？”这类逻辑或常识性问题。\n-文化常识，如文学作品的作者、电影的演员。\n-个人建议或常见问题解答，如“如何修复自行车轮胎？”或者“如何煮意面？”。\n-出行计划或活动方案，如“帮我列出麻婆豆腐的菜谱”。
        2、如果问题涉及以下情况，请回复'Bing'.\n-最新新闻事件或最近发生的事情。\n-特定日期或时间点之后的信息更新，如“最新的奥斯卡获奖名单”或“2024年的美国总统是谁？”。\n-实时数据或统计，如股票市场、体育比赛结果、天气预报。\n-特定个人的近期动态或社交媒体更新。\n-高度专业化或地域性的问题，如特定产品的用户评价、某地的餐馆推荐。
        3、对于知识库有关的问题，请回复'RAG'.
        现在，请根据上述标准来判断用户问题的具体类别。
        示例回复：
        {{
            "intent": "RAG"
        }}"""
        user_id = f"single_turn_user_{int(time.time())}"
        group_id = f"single_turn_group_{int(time.time())}"
        user_query = "我想查询北京明天的天气预报"
        
        logger.info(f"===== 测试用例：单轮对话 ===== 用户ID: {user_id}")
        
        # 使用await等待异步函数执行完成
        result = await self.conversation_manager.process_message_intent(system_prompt, user_query, user_id, group_id)
        
        # 验证结果
        self.assertIn('response', result)
        self.assertIn('conversation_id', result)
        self.assertIn('is_new_conversation', result)
        self.assertTrue(result['is_new_conversation'])  # 应该是新对话
        
        # 验证对话历史
        conversation_id, history, intent = self.conversation_manager.get_conversation_history(user_id, group_id)
        self.assertEqual(result['conversation_id'], conversation_id)
        self.assertEqual(1, len(history))  # 应该只有一条消息
        self.assertEqual(user_query, history[0]["user_query"])
        
        print(f"单轮对话 - 用户: {user_query}")
        print(f"单轮对话 - 回复: {result['response']}")
        print(f"单轮对话 - 会话ID: {result['conversation_id']}")
        
        # 清理测试数据
        self.conversation_manager.clear_conversation(user_id, group_id)
        logger.info("单轮对话测试完成，已清理测试数据")


if __name__ == "__main__":
    # 运行测试
    logger.info("开始运行chatmodel_service测试")
    
    # 运行单元测试
    unittest.main(exit=False)