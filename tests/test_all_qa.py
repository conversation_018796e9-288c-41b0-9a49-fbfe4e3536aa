#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ALLQA 功能测试脚本
测试 ALLQA 类的集合选择功能
"""

import asyncio
import sys
import os
from typing import List
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.all_qa_config import COLLECTION_MAPPING, get_collections_by_types
from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")


async def test_collection_mapping():
    """测试集合映射功能"""
    print("=" * 50)
    print("测试集合映射功能")
    print("=" * 50)
    
    # 测试不同的集合组合
    test_cases = [
        [],  # 空列表，应该返回所有集合
        ["data"],  # 只搜索数据集合
        ["hardware"],  # 只搜索硬件集合
        ["car"],  # 只搜索汽车集合
        ["data", "hardware"],  # 搜索数据和硬件集合
        ["car", "hardware"],  # 搜索汽车和硬件集合
        ["data", "car"],  # 搜索数据和汽车集合
        ["data", "hardware", "car"],  # 搜索所有集合
        ["invalid"],  # 无效集合类型
        ["data", "invalid", "car"],  # 混合有效和无效集合类型
    ]
    
    for i, collection_types in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {collection_types}")
        collections = get_collections_by_types(collection_types)
        print(f"返回的集合数量: {len(collections)}")
        
        # 显示集合名称
        collection_names = [c.get("collection_name", "Unknown") for c in collections]
        print(f"集合名称: {collection_names}")


async def test_allqa_with_collections():
    """测试 ALLQA 类的集合选择功能（简化版本）"""
    print("\n" + "=" * 50)
    print("测试 ALLQA 类的集合选择功能（配置测试）")
    print("=" * 50)

    print("注意：由于环境限制，此测试只验证配置功能，不进行实际的模型调用")

    # 测试不同的集合配置
    test_configs = [
        {
            "name": "默认配置（所有集合）",
            "collection": None,
            "description": "不指定集合，使用所有可用集合"
        },
        {
            "name": "只搜索数据集合",
            "collection": ["data"],
            "description": "只在DATA_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "只搜索硬件集合",
            "collection": ["hardware"],
            "description": "只在HARDWARE_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "只搜索汽车集合",
            "collection": ["car"],
            "description": "只在CAR_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "搜索汽车和硬件集合",
            "collection": ["car", "hardware"],
            "description": "在CAR_SEARCH_COLLECTIONS和HARDWARE_SEARCH_COLLECTIONS中搜索"
        },
        {
            "name": "搜索数据和汽车集合",
            "collection": ["data", "car"],
            "description": "在DATA_SEARCH_COLLECTIONS和CAR_SEARCH_COLLECTIONS中搜索"
        }
    ]

    for i, config in enumerate(test_configs, 1):
        print(f"\n测试配置 {i}: {config['name']}")
        print(f"描述: {config['description']}")
        print(f"集合参数: {config['collection']}")

        # 测试集合映射功能
        try:
            collections = get_collections_by_types(config['collection']) if config['collection'] else get_collections_by_types([])
            print(f"✅ 配置解析成功，返回 {len(collections)} 个集合")

            # 显示前几个集合名称
            collection_names = [c.get("collection_name", "Unknown") for c in collections[:3]]
            if len(collections) > 3:
                collection_names.append("...")
            print(f"   集合名称: {collection_names}")

        except Exception as e:
            print(f"❌ 配置解析失败: {e}")


async def test_invalid_collections():
    """测试无效集合参数的处理"""
    print("\n" + "=" * 50)
    print("测试无效集合参数的处理")
    print("=" * 50)
    
    # 测试无效集合类型
    invalid_cases = [
        ["invalid_type"],
        ["data", "invalid_type"],
        [""],  # 空字符串
    ]
    
    # 单独处理包含None的情况
    none_cases = [
        [None],  # None值
    ]
    
    for i, invalid_collection in enumerate(invalid_cases, 1):
        print(f"\n无效测试用例 {i}: {invalid_collection}")
        try:
            collections = get_collections_by_types(invalid_collection)
            print(f"返回的集合数量: {len(collections)}")
            print("处理成功（应该返回默认集合或空集合）")
        except Exception as e:
            print(f"处理失败: {e}")
    
    # 处理包含None的特殊情况
    for i, none_collection in enumerate(none_cases, len(invalid_cases) + 1):
        print(f"\n无效测试用例 {i}: {none_collection}")
        try:
            # 过滤掉None值
            filtered_collection = [item for item in none_collection if item is not None]
            collections = get_collections_by_types(filtered_collection)
            print(f"返回的集合数量: {len(collections)}")
            print("处理成功（过滤None值后处理）")
        except Exception as e:
            print(f"处理失败: {e}")


async def main():
    """主测试函数"""
    print("开始 ALLQA 功能测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    
    try:
        # 测试集合映射功能
        await test_collection_mapping()
        
        # 测试 ALLQA 类的集合选择功能
        await test_allqa_with_collections()
        
        # 测试无效集合参数的处理
        await test_invalid_collections()
        
        print("\n" + "=" * 50)
        print("所有测试完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        logger.exception("详细错误信息:")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
