import sys
import os
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from pipelines.rag_qa import RAGQA
import pytest

@pytest.mark.asyncio
async def test_ragqa_single_turn():
    """测试RAGQA单轮对话"""
    ragqa = RAGQA(model_id="qwen3_32b", request_id="req_single")
    query = "FPC点胶规范"
    user_id = "test_user_single"
    history = []
    result = await ragqa.generate(query, user_id, history)
    print("单轮对话结果:", result)
    assert hasattr(result, "choices") or hasattr(result, "content")

@pytest.mark.asyncio
async def test_ragqa_multi_turn():
    """测试RAGQA多轮对话"""
    ragqa = RAGQA(model_id="qwen3_32b", request_id="req_multi")
    user_id = "test_user_multi"
    # 模拟多轮历史
    history = [
        {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
        {"query": "FPC点胶规范", "content": "点胶规范是..."}
    ]
    query = "点胶厚度要求是多少？"
    result = await ragqa.generate(query, user_id, history)
    print("多轮对话结果:", result)
    assert hasattr(result, "choices") or hasattr(result, "content")

@pytest.mark.asyncio
async def test_ragqa_stream_single_turn():
    """测试RAGQA单轮对话流式输出"""
    ragqa = RAGQA(model_id="qwen3_32b", request_id="req_stream_single")
    query = "FPC点胶规范"
    user_id = "test_user_stream_single"
    history = []
    chunks = []
    async for chunk in ragqa.generate_stream(query, user_id, history):
        print("流式单轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    # 检查每个chunk有choices或content字段
    for chunk in chunks:
        assert "choices" in chunk or "content" in chunk

@pytest.mark.asyncio
async def test_ragqa_stream_multi_turn():
    """测试RAGQA多轮对话流式输出"""
    ragqa = RAGQA(model_id="qwen3_32b", request_id="req_stream_multi")
    user_id = "test_user_stream_multi"
    history = [
        {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
        {"query": "FPC点胶规范", "content": "点胶规范是..."}
    ]
    query = "点胶厚度要求是多少？"
    chunks = []
    async for chunk in ragqa.generate_stream(query, user_id, history):
        print("流式多轮chunk:", chunk)
        chunks.append(chunk)
    assert len(chunks) > 0
    for chunk in chunks:
        assert "choices" in chunk or "content" in chunk

if __name__ == "__main__":
    # asyncio.run(test_ragqa_single_turn())
    # asyncio.run(test_ragqa_multi_turn())
    asyncio.run(test_ragqa_stream_single_turn())
    asyncio.run(test_ragqa_stream_multi_turn())
