"""意图识别配置文件"""
import os
CURRENT_INTENT_MODEL = os.getenv("INTENT_MODEL", "intent_gpt_4o")

# 所有可用的意图模型配置
INTENT_MODELS = {
    "intent_qwen3_8b": {
        "llm_service": "chatmodel",
        "model_id": "qwen3_8b",
        "max_retries": 3,
        "retry_delay": 1,
        "timeout": 30,
    },
    "intent_qwen3_32b": {
        "llm_service": "chatmodel",
        "model_id": "qwen3_32b",
        "max_retries": 3,
        "retry_delay": 1,
        "timeout": 30,
    },
    "intent_gpt_4o": {
        "llm_service": "chatflow",
        "model_id": "gpt_4o",
        "max_retries": 3,
        "retry_delay": 1,
        "timeout": 30,
    }
}

# 当前使用的意图模型配置
INTENT_MODEL_CONFIG = INTENT_MODELS[CURRENT_INTENT_MODEL]

# 意图识别结果校验配置
INTENT_VALIDATION = {
    "valid_intents": ["bing", "RAG", "LLM", "other"],
    "default_intent": {
        "intent_type": "other",
        "confidence": 0.0,
        "description": "未能识别有效意图"
    }
}

# 意图识别最大对话轮数配置
MAX_INTENT_CONVERSATION_ROUNDS = 5  # 对话轮数，默认5轮

# 意图识别结果缓存配置
INTENT_CACHE = {
    "enabled": True,  # 是否启用缓存
    "expiration": 3600,  # 缓存过期时间（秒），默认1小时
}