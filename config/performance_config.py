import os
from typing import Dict, Any

# 搜索服务性能配置
SEARCH_PERFORMANCE_CONFIG = {
    # 连接池配置
    "connection_pool": {
        "max_keepalive_connections": int(os.getenv("SEARCH_MAX_KEEPALIVE_CONNECTIONS", "20")),
        "max_connections": int(os.getenv("SEARCH_MAX_CONNECTIONS", "100")),
        "connect_timeout": float(os.getenv("SEARCH_CONNECT_TIMEOUT", "10.0")),
        "read_timeout": float(os.getenv("SEARCH_READ_TIMEOUT", "60.0")),
    },
    
    # 缓存配置
    "cache": {
        "enabled": os.getenv("SEARCH_CACHE_ENABLED", "true").lower() == "true",
        "ttl_seconds": int(os.getenv("SEARCH_CACHE_TTL", "300")),  # 5分钟
        "max_size": int(os.getenv("SEARCH_CACHE_MAX_SIZE", "1000")),  # 最大缓存条目数
        "cleanup_interval": int(os.getenv("SEARCH_CACHE_CLEANUP_INTERVAL", "60")),  # 清理间隔（秒）
    },
    
    # 批量处理配置
    "batch": {
        "max_batch_size": int(os.getenv("SEARCH_MAX_BATCH_SIZE", "50")),
        "batch_timeout": float(os.getenv("SEARCH_BATCH_TIMEOUT", "30.0")),
        "concurrent_limit": int(os.getenv("SEARCH_CONCURRENT_LIMIT", "10")),
    },
    
    # 日志配置
    "logging": {
        "log_level": os.getenv("SEARCH_LOG_LEVEL", "INFO"),
        "log_requests": os.getenv("SEARCH_LOG_REQUESTS", "false").lower() == "true",
        "log_responses": os.getenv("SEARCH_LOG_RESPONSES", "false").lower() == "true",
    },
    
    # 重试配置
    "retry": {
        "enabled": os.getenv("SEARCH_RETRY_ENABLED", "true").lower() == "true",
        "max_retries": int(os.getenv("SEARCH_MAX_RETRIES", "3")),
        "retry_delay": float(os.getenv("SEARCH_RETRY_DELAY", "1.0")),
        "backoff_factor": float(os.getenv("SEARCH_BACKOFF_FACTOR", "2.0")),
    },
    
    # 监控配置
    "monitoring": {
        "enabled": os.getenv("SEARCH_MONITORING_ENABLED", "true").lower() == "true",
        "metrics_interval": int(os.getenv("SEARCH_METRICS_INTERVAL", "60")),  # 指标收集间隔（秒）
        "performance_threshold": float(os.getenv("SEARCH_PERFORMANCE_THRESHOLD", "5.0")),  # 性能阈值（秒）
    }
}

# 数据搜索性能配置（针对DATA_QA的特殊优化）
DATA_SEARCH_PERFORMANCE_CONFIG = {
    **SEARCH_PERFORMANCE_CONFIG,
    "cache": {
        **SEARCH_PERFORMANCE_CONFIG["cache"],
        "ttl_seconds": int(os.getenv("DATA_SEARCH_CACHE_TTL", "600")),  # 10分钟，数据查询缓存时间更长
    },
    "batch": {
        **SEARCH_PERFORMANCE_CONFIG["batch"],
        "max_batch_size": int(os.getenv("DATA_SEARCH_MAX_BATCH_SIZE", "100")),  # 数据查询支持更大的批量
    }
}

# 性能优化建议配置
PERFORMANCE_TIPS = {
    "search_service": [
        "启用缓存可以显著提高重复查询的性能",
        "使用批量搜索可以减少网络开销",
        "适当调整连接池大小可以平衡内存使用和并发性能",
        "监控缓存命中率来优化缓存策略",
        "使用异步上下文管理器确保资源正确释放"
    ],
    "data_qa": [
        "DATA_QA使用多个集合，建议启用并发搜索",
        "数据查询结果相对稳定，可以设置更长的缓存时间",
        "考虑使用批量搜索来减少多个集合的查询开销"
    ]
}

def get_performance_config(service_type: str = "default") -> Dict[str, Any]:
    """获取性能配置"""
    if service_type == "data_search":
        return DATA_SEARCH_PERFORMANCE_CONFIG
    return SEARCH_PERFORMANCE_CONFIG

def validate_performance_config(config: Dict[str, Any]) -> bool:
    """验证性能配置的有效性"""
    try:
        # 验证连接池配置
        pool_config = config.get("connection_pool", {})
        assert pool_config["max_keepalive_connections"] > 0
        assert pool_config["max_connections"] >= pool_config["max_keepalive_connections"]
        assert pool_config["connect_timeout"] > 0
        assert pool_config["read_timeout"] > 0
        
        # 验证缓存配置
        cache_config = config.get("cache", {})
        assert cache_config["ttl_seconds"] > 0
        assert cache_config["max_size"] > 0
        
        # 验证批量配置
        batch_config = config.get("batch", {})
        assert batch_config["max_batch_size"] > 0
        assert batch_config["concurrent_limit"] > 0
        
        return True
    except (KeyError, AssertionError):
        return False 