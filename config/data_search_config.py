import os
import json
#search_mode: 搜索模式，可指定为text, dense, sparse, hybrid, hybrid_rrf，不指定时，默认使用dense模式
DATA_SEARCH_MODELS = {
    "default_search": {
        "api_url": os.getenv("DATA_SEARCH_API_URL", "http://localhost:8000"),
        "collection_name": os.getenv("DATA_SEARCH_COLLECTION", "default_collection"),
        "token": os.getenv("DATA_SEARCH_TOKEN", "default_token"),
        "db_name": os.getenv("DATA_SEARCH_DB_NAME", "default_db"),
        "default_params": {
            "top_k": 60,
            "search_mode": "hybrid",
            "fusion_method": "rrf",
            "sparse_weight": 0.7,
            "dense_weight": 0.3,
            "rrf_k": 60
        }
    }
}
CURRENT_DATA_SEARCH_MODEL = os.getenv("SEARCH_MODEL", "default_search")
DATA_SEARCH_MODEL_CONFIG = DATA_SEARCH_MODELS[CURRENT_DATA_SEARCH_MODEL]

# 重排模型配置
DATA_RERANK_MODELS = {
    "default_rerank": {
        "api_url": os.getenv("RERANK_API_URL", "http://localhost:9000"),
        "api_key": os.getenv("RERANK_API_KEY", "test-key"),
        "default_params": {
            "top_r": 20,
            "min_score": 0.5
        },
        "environment": ""
    },
    "Qwen3-0.6B-Reranker_CL": {
        "api_url": os.getenv("Qwen3_0.6B_Reranker_CL_RERANK_API_URL", ""),
        "api_key": os.getenv("Qwen3_0.6B_Reranker_CL_RERANK_API_KEY", ""),
        "default_params": {
            "top_r": 20,
            "min_score": 0.5
        },
        "environment": "cloudml"
    }
}
CURRENT_RERANK_MODEL = os.getenv("RERANK_MODEL", "default_rerank")
DATA_RERANK_MODEL_CONFIG = DATA_RERANK_MODELS[CURRENT_RERANK_MODEL]

# 支持环境变量为JSON字符串或逗号分隔字符串
def parse_collections(val):
    if not val:
        return [{"collection_name": "collection1"}]
    try:
        # 尝试解析为JSON
        collections = json.loads(val)
        # 如果是字符串列表，转为字典列表
        if isinstance(collections, list) and all(isinstance(x, str) for x in collections):
            return [{"collection_name": x} for x in collections]
        # 如果已经是字典列表，直接返回
        if isinstance(collections, list) and all(isinstance(x, dict) for x in collections):
            return collections
    except Exception:
        # 逗号分隔字符串
        return [{"collection_name": x.strip()} for x in val.split(",") if x.strip()]
    # 兜底
    return [{"collection_name": "collection1"}]

DATA_SEARCH_COLLECTIONS = parse_collections(os.getenv("DATA_SEARCH_COLLECTIONS"))
