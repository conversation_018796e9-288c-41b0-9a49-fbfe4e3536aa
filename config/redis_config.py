"""
Redis配置文件
包含Redis连接的配置信息
"""
import os
import sys
from typing import Optional
from pydantic import Field, BaseModel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.keycenter_decode import kc_decode

class RedisConfig(BaseModel):
    """Redis配置类"""
    REDIS_HOST: str = Field(default="localhost")
    REDIS_PORT: int = Field(default=6379)
    REDIS_DB: int = Field(default=0)
    REDIS_PASSWORD: Optional[str] = Field(default=None)
    REDIS_USERNAME: Optional[str] = Field(default=None)
    REDIS_SSL: bool = Field(default=False)
    REDIS_TIMEOUT: int = Field(default=900)  # 连接超时时间（秒）

def get_redis_config() -> RedisConfig:
    """
    获取Redis配置
    
    从环境变量中读取配置，如果环境变量不存在，则使用默认值
    
    Returns:
        RedisConfig: Redis配置对象
    """
    return RedisConfig(
        REDIS_HOST=os.environ.get("REDIS_HOST", "localhost"),
        REDIS_PORT=int(os.environ.get("REDIS_PORT", 6379)),
        REDIS_DB=int(os.environ.get("REDIS_DB", 0)),
        REDIS_PASSWORD=kc_decode(os.environ.get("REDIS_PASSWORD"), os.environ.get("kc_domain", "localhost")),
        REDIS_USERNAME=os.environ.get("REDIS_USERNAME"),
        REDIS_SSL=os.environ.get("REDIS_SSL", "").lower() == "true",
        REDIS_TIMEOUT=int(os.environ.get("REDIS_TIMEOUT", 10))
    )

def get_redis_connection_params() -> dict:
    """
    获取Redis连接参数
    
    Returns:
        dict: Redis连接参数字典
    """
    config = get_redis_config()
    params = {
        "host": config.REDIS_HOST,
        "port": config.REDIS_PORT,
        "socket_timeout": config.REDIS_TIMEOUT
    }
    
    # 添加可选参数
    if config.REDIS_PASSWORD:
        params["password"] = config.REDIS_PASSWORD
    if config.REDIS_USERNAME:
        params["username"] = config.REDIS_USERNAME
    if config.REDIS_SSL:
        params["ssl"] = True
        params["ssl_cert_reqs"] = None  # 不验证证书
    
    return params