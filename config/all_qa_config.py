import os
import json
from typing import Dict, List 

# 搜索模型配置（复用hardware_search_config的配置）
from config.hardware_search_config import HARDWARE_SEARCH_COLLECTIONS

#search_mode: 搜索模式，可指定为text, dense, sparse, hybrid, hybrid_rrf，不指定时，默认使用dense模式
SEARCH_MODELS = {
    "default_search": {
        "api_url": os.getenv("HARDWARE_SEARCH_API_URL", "http://localhost:8000"),
        "collection_name": os.getenv("HARDWARE_SEARCH_COLLECTION", "default_collection"),
        "token": os.getenv("HARDWARE_SEARCH_TOKEN", "default_token"),
        "db_name": os.getenv("HARDWARE_SEARCH_DB_NAME", "default_db"),
        "default_params": {
            "top_k": 20,
            "search_mode": "hybrid",
            "fusion_method": "rrf",
            "sparse_weight": 0.3,
            "dense_weight": 0.7,
            "rrf_k": 60
        }
    }
}
CURRENT_SEARCH_MODEL = os.getenv("SEARCH_MODEL", "default_search")
ALL_QA_SEARCH_CONFIG = SEARCH_MODELS[CURRENT_SEARCH_MODEL]

# 检索库配置（复用data_search_config的配置）
from config.data_search_config import DATA_SEARCH_COLLECTIONS

# 汽车知识库配置
from config.car_search_config import CAR_SEARCH_COLLECTIONS

# 重排模型配置（复用data_search_config的配置）
DATA_RERANK_MODELS = {
    "default_rerank": {
        "api_url": os.getenv("RERANK_API_URL", "http://localhost:9000"),
        "api_key": os.getenv("RERANK_API_KEY", "test-key"),
        "default_params": {
            "top_r": 20,
            "min_score": 0.5
        },
        "environment": ""
    },
    "Qwen3-0.6B-Reranker_CL": {
        "api_url": os.getenv("Qwen3_0.6B_Reranker_CL_RERANK_API_URL", ""),
        "api_key": os.getenv("Qwen3_0.6B_Reranker_CL_RERANK_API_KEY", ""),
        "default_params": {
            "top_r": 20,
            "min_score": 0.5
        },
        "environment": "cloudml"
    }
}
CURRENT_RERANK_MODEL = os.getenv("RERANK_MODEL", "default_rerank")
ALL_QA_RERANK_CONFIG = DATA_RERANK_MODELS[CURRENT_RERANK_MODEL]

ALL_SEARCH_COLLECTIONS = DATA_SEARCH_COLLECTIONS + HARDWARE_SEARCH_COLLECTIONS + CAR_SEARCH_COLLECTIONS

# 集合映射配置 - 定义标识符与对应COLLECTIONS的映射关系
COLLECTION_MAPPING = {
    "data": DATA_SEARCH_COLLECTIONS,
    "hardware": HARDWARE_SEARCH_COLLECTIONS,
    "car": CAR_SEARCH_COLLECTIONS
}

def get_collections_by_types(collection_types: List[str]) -> List[Dict]:
    """
    根据集合类型列表获取对应的collections

    Args:
        collection_types: 集合类型列表，如 ["car", "hardware"] 或 ["data"]

    Returns:
        合并后的collections列表
    """
    if not collection_types:
        return ALL_SEARCH_COLLECTIONS

    merged_collections = []
    for collection_type in collection_types:
        if collection_type in COLLECTION_MAPPING:
            merged_collections.extend(COLLECTION_MAPPING[collection_type])
        else:
            # 如果类型不存在，记录警告但不中断
            print(f"Warning: Unknown collection type '{collection_type}', skipping...")

    return merged_collections if merged_collections else ALL_SEARCH_COLLECTIONS