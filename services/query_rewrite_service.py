import re
import logging
from typing import List, Dict, Any, Optional
from collections import Counter
from loguru import logger
from config.logging_config import configure_logging

configure_logging()

class QueryRewriteService:
    """问题改写服务类"""
    
    def __init__(self, request_id: str = None):
        """初始化问题改写服务"""
        self.logger = logger.bind(request_id=request_id)
        
        # 定义代码模式
        self.CODE_PREFIX_PATTERN = r'(?:错误代码|故障代码|故障编码|代码|编码|编号|序列|型号|版本|错误|故障|报警|报警代码|报错|报错代码|Fault|fault|F)'
        self.CODE_VALUE_PATTERN = r'(?:\d+|[A-Za-z]+\d+)'
        self.CODE_PATTERN = re.compile(f'({self.CODE_PREFIX_PATTERN}[ \t]*{self.CODE_VALUE_PATTERN})(?:[:：]|\"|\'|“｜‘)?')
        self.STANDALONE_CODE_PATTERN = re.compile(r'([A-Za-z]+[0-9]+[A-Za-z0-9-_]*|[0-9]+[A-Za-z]+[A-Za-z0-9-_]*|[A-Za-z0-9]+[-_][A-Za-z0-9]+(?:[-_][A-Za-z0-9]+)*)')
        self._CODE_VALUE_REGEX = re.compile(f'{self.CODE_PREFIX_PATTERN}[ \t]*({self.CODE_VALUE_PATTERN})')
        
        # 停用词集合
        self.STOPWORDS = self._load_default_stopwords()
        
        self.logger.info("QueryRewriteService 初始化完成")
    
    def _load_default_stopwords(self) -> set:
        """加载默认停用词"""
        return {
            "的", "了", "和", "是", "在", "我", "有", "你", "他", "她", "它", "们", "这", "那", "哪",
            "什么", "怎么", "如何", "为什么", "啊", "吗", "呢", "吧", "啦", "呀", "哦", "哈", "嗯",
            "一个", "一些", "一下", "一种", "一样", "一起", "一直", "一点", "一定", "一般", "一边",
            "不", "不是", "不会", "不能", "不要", "不过", "与", "且", "但", "但是", "而", "而且",
            "因为", "所以", "如果", "就", "就是", "很", "得", "着", "过", "吧", "把", "被", "让",
            "从", "向", "到", "给", "对", "对于", "为", "为了", "由", "由于", "以", "以及", "或",
            "或者", "也", "也是", "也许", "了", "啊", "可", "可以", "可能", "来", "去", "又", "再",
            "还", "还是", "只", "只是", "中", "中间", "之", "之前", "之后", "之间", "自己", "其",
            "其中", "其他", "其实", "则", "能", "都", "那", "那么", "那些", "那样", "这", "这个",
            "这些", "这样", "谁", "什么", "哪", "哪些", "哪个", "如", "如何", "如果", "如此", "当",
            "当然", "时", "时候", "最", "比", "比如", "然后", "已经", "还有", "虽然", "但是", "并",
            "并且", "却", "却是", "吗", "呢", "吧", "啊", "啦", "呀", "吧", "呢", "了", "哦", "哈",
            # 空白字符、标点符号等
            " ", "\t", "\n", "\r", "\f", "\v", "　",  # 各种空白字符，包括全角空格
            ".", ",", ";", ":", "?", "!", "，", "。", "；", "：", "？", "！", 
            "、", "…", "...", "\"", "'", """, """, "'", "'", "(", ")", "（", "）",
            "[", "]", "【", "】", "{", "}", "《", "》", "<", ">", "«", "»", 
            "+", "-", "*", "/", "=", "%", "$", "#", "@", "&", "|", "\\", "^", "~",
            # 特别添加#符号作为停用词
            "#", "##", "###", "####", "#####",
            # 添加引号和圆点符号
            "“", "•"
    }

    def extract_and_process_codes(self, text: str) -> List[str]:
        """提取并处理文本中的独立字母数字混合代码"""
        matches = self.STANDALONE_CODE_PATTERN.findall(text)
        unique_codes = {match for match in matches if match}
        return [f"CODE_{code}" for code in unique_codes]

    def normalize_code_terms(self, text: str) -> List[str]:
        """提取文本中的代码术语并标准化为CODE_数字格式"""
        code_terms = self.CODE_PATTERN.findall(text)
        return [
            f"CODE_{match.group(1)}" 
            for term in code_terms 
            if term and (match := self._CODE_VALUE_REGEX.search(term))
        ]

    def custom_tokenize(self, text: str) -> tuple:
        """自定义分词函数，增强对数字代码的处理"""
        try:
            import jieba
            jieba.setLogLevel(logging.INFO)
            
            # 使用jieba进行基础分词并过滤停用词
            filtered_tokens = [token.strip() for token in jieba.lcut(text) 
                             if token.strip() and token.strip() not in self.STOPWORDS]
            
            # 提取并处理所有代码
            prefixed_codes = self.normalize_code_terms(text)
            standalone_codes = self.extract_and_process_codes(text)
            
            # 合并代码tokens
            code_tokens = list(set(prefixed_codes) | set(standalone_codes))
            
            return filtered_tokens, code_tokens
            
        except ImportError:
            self.logger.warning("jieba未安装，使用简单分词")
            # 简单分词作为备选
            tokens = text.split()
            filtered_tokens = [token for token in tokens if token not in self.STOPWORDS]
            
            prefixed_codes = self.normalize_code_terms(text)
            standalone_codes = self.extract_and_process_codes(text)
            code_tokens = list(set(prefixed_codes) | set(standalone_codes))
            
            return filtered_tokens, code_tokens

    def generate_number_variants(self, letter_digit_code: str) -> List[str]:
        """为字母+数字代码生成多种数字变体"""
        digit_match = re.search(r'\d+', letter_digit_code)
        if not digit_match:
            return [letter_digit_code]
        
        digit_str = digit_match.group()
        digit_num = int(digit_str)
        variants = [digit_str]
        
        # 对少于3位的数字补零
        if len(digit_str) < 3:
            if len(digit_str) == 1:
                variants.append(f"{digit_num:02d}")
            variants.append(f"{digit_num:03d}")
        
        # 去重并保持顺序
        return list(dict.fromkeys(variants))

    def replace_codes_in_text(self, text: str, codes: List[str]) -> List[str]:
        """根据已提取的codes进行替换，将字母+数字代码替换成纯数字的多种变体"""
        # 创建替换映射
        replacement_map = {}
        for code in codes:
            if code.startswith("CODE_"):
                original_code = code[5:]  # 去掉"CODE_"前缀
                if re.match(r'^[Ff]\d+$', original_code):  # 只对以F或f开头的数字代码进行替换
                    replacement_map[original_code] = self.generate_number_variants(original_code)

        if not replacement_map:
            return []
        
        # 按key长度降序排序，避免短代码被长代码的子串替换
        sorted_codes = sorted(replacement_map.keys(), key=len, reverse=True)
        
        # 生成所有可能的替换组合
        result_texts = [text]
        
        for code in sorted_codes:
            new_texts = []
            for current_text in result_texts:
                for variant in replacement_map[code]:
                    new_text = current_text.replace(code, variant)
                    new_texts.append(new_text)
            result_texts = new_texts
        
        return result_texts

    def rewrite_query(self, query: str) -> Dict[str, Any]:
        """问题改写主接口"""
        self.logger.info(f"开始处理查询改写: {query}")
        
        try:
            # 提取代码
            segments, codes = self.custom_tokenize(query)
            
            # 生成文本变体
            variants = self.replace_codes_in_text(query, codes)
            
            result = {
                "original_query": query,
                "extracted_codes": codes,
                "tokenized_segments": segments,
                "query_variants": variants,
                "variant_count": len(variants)
            }
            
            self.logger.info(f"查询改写完成，生成 {len(variants)} 个变体")
            return result
            
        except Exception as e:
            self.logger.error(f"查询改写失败: {e}")
            return {
                "original_query": query,
                "extracted_codes": [],
                "tokenized_segments": [],
                "query_variants": [],
                "variant_count": 0,
                "error": str(e)
            }

    def batch_rewrite_queries(self, queries: List[str]) -> List[Dict[str, Any]]:
        """批量处理查询改写"""
        self.logger.info(f"开始批量处理 {len(queries)} 个查询")
        
        results = []
        for i, query in enumerate(queries):
            self.logger.debug(f"处理第 {i+1}/{len(queries)} 个查询")
            result = self.rewrite_query(query)
            results.append(result)
        
        self.logger.info(f"批量处理完成，共处理 {len(results)} 个查询")
        return results
