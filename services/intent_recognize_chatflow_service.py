#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""ChatFlow意图识别服务，支持基于conversation_id的连续对话意图识别。"""
import sys
import os
import json
import time
import uuid
import re
from typing import Dict, List, Optional, Tuple, Union, Any
from loguru import logger
import logging
import redis
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from services.chat_storage_service import ConversationHistoryStorage
from prompts.intent_prompt import system_prompt_1, system_prompt_multi_turn
from config.intent_config import INTENT_MODEL_CONFIG, INTENT_VALIDATION, INTENT_CACHE
from config.chat_config import CONVERSATION_TIMEOUT
from config.intent_config import MAX_INTENT_CONVERSATION_ROUNDS


class ChatFlowIntentRecognitionError(Exception):
    """意图识别相关的自定义异常"""
    pass

class ChatFlowBaseIntentService:
    """基础意图识别服务"""

    def __init__(self, model_provider, request_id: str = None):
        self.model = model_provider
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
    async def generate_intent(self, system_prompt: str, user_query: str, conversation_id: str = None, user_id: str = "abc-123") -> Dict[str, Any]:
        """生成意图识别结果"""
            # 参考chatflow_service的调用方式
        try:
            response = await self.model.generate(
                query=user_query,
                response_mode="blocking",
                user_id=user_id,
                inputs={"system_prompt": system_prompt},
                conversation_id=conversation_id
            )
            conversation_id = response.get("conversation_id")
            meta_data = response.get("metadata")
            answer = response.get("answer")
            text_content = self._remove_think(answer)
            intent = await self._extract_intent(text_content)
            return {
                "response": text_content,
                "intent": intent,
                "conversation_id": conversation_id,
                "model_usage": self._extract_model_usage(response),
            }
        except Exception as e:
            self.logger.error(f"意图识别失败: {str(e)}")
            raise ChatFlowIntentRecognitionError(f"意图识别模型调用失败: {str(e)}")
            
    def _remove_think(self, text_content: str) -> str:
        """移除<think>标签及其内容"""
        if not text_content:
            return ""
        pattern = r'<think>.*?</think>'
        new_text_content = re.sub(pattern, '', text_content, flags=re.DOTALL)
        return new_text_content
    def _extract_model_usage(self, response: dict) -> dict:
        """提取模型使用情况"""
        return {
            "total_tokens": response.get("metadata", {}).get("usage", {}).get("total_tokens", 0),
            "prompt_tokens": response.get("metadata", {}).get("usage", {}).get("prompt_tokens", 0),
            "completion_tokens": response.get("metadata", {}).get("usage", {}).get("completion_tokens", 0),
            "elapsed_time": response.get("metadata", {}).get("usage", {}).get("latency", 0),
            "created_at": datetime.utcfromtimestamp(response.get("created_at", 0)).isoformat()
        }

    async def _extract_intent(self, text: str) -> Optional[Dict]:
        """从响应文本中提取意图JSON"""
        if not text:
            return None
        clean_text = text.replace("```json", "").replace("```", "").strip()
        try:
            return json.loads(clean_text)
        except Exception as e:
            self.logger.warning(f"意图JSON解析失败: {str(e)}")
            return None



class ChatFlowIntentRecognizer:
    """意图识别类，支持多轮对话管理+意图识别"""

    def __init__(self, redis_client: Optional[redis.StrictRedis] = None, request_id: str = None):
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        self.conversation_timeout = CONVERSATION_TIMEOUT
        self.max_intent_conversation_rounds = MAX_INTENT_CONVERSATION_ROUNDS
        self.history_storage = ConversationHistoryStorage(redis_client, self.conversation_timeout)
        llm_provider = get_llm_provider(INTENT_MODEL_CONFIG["model_id"])
        self.model_service = ChatFlowBaseIntentService(llm_provider, request_id)
        self.cache_enabled = INTENT_CACHE["enabled"]
        self.cache_expiration = INTENT_CACHE["expiration"]
        self.logger.info(f"ChatFlow意图识别器初始化完成: {request_id}")

    def is_new_conversation(self, user_id: str, group_id: str) -> bool:
        """判断是否为新对话"""
        if not self.history_storage.check_intent_key_exists(user_id, group_id):
            return True
        _, last_time = self.history_storage.get_conversation_intent(user_id, group_id)
        if last_time and (time.time() - last_time > self.conversation_timeout):
            self.history_storage.delete_conversation_data(user_id, group_id)
            return True
        return False

    def get_conversation_history(self, user_id: str, group_id: str) -> Tuple[Optional[str], List[Dict], Optional[Dict]]:
        """获取对话历史"""
        if self.is_new_conversation(user_id, group_id):
            return None, [], None
        convesation_intent, _ = self.history_storage.get_conversation_intent(user_id, group_id)
        if convesation_intent:
            data = json.loads(convesation_intent)
            # self.logger.info(f"获取对话历史: {json.dumps(data, ensure_ascii=False)}")
            return (
                data.get("conversation_id"),
                data.get("history", []),
                data.get("intent", {})
            )
        return None, [], None

    def build_messages(self, system_prompt: str, user_query: str, history: List[Dict]) -> Tuple[str, str]:
        """构建系统提示和用户查询（ChatFlow模型接口不需要历史拼接）"""
        # 这里只返回最新的system_prompt和user_query，历史对话由mify管理
        return system_prompt, user_query

    def save_conversation(self, user_id: str, group_id: str, conversation_id: str, query: str, response: str, intent: Dict) -> None:
        """保存对话记录"""
        _, history, _ = self.get_conversation_history(user_id, group_id)
        history.append({
            "user_query": query,
            "intent": intent,
            "timestamp": time.time()
        })
        data = {
            "conversation_id": conversation_id,
            "history": history,
            "intent": intent
        }
        self.logger.info(f"保存对话记录: {json.dumps(data, ensure_ascii=False)}")
        self.history_storage.save_conversation_intent(
            user_id,
            group_id,
            json.dumps(data)
        )

    async def recognize_intent(self, user_id: str, group_id: str, query: str) -> Dict:
        """识别用户查询的意图"""
        try:
            is_new = self.is_new_conversation(user_id, group_id)
            conversation_id, history, previous_intent = self.get_conversation_history(user_id, group_id)
            system_prompt = system_prompt_1 if is_new else system_prompt_multi_turn
            sys_prompt, user_query = self.build_messages(system_prompt, query, history)
            result = await self.model_service.generate_intent(
                sys_prompt, user_query, conversation_id, user_id
            )
            intent = result["intent"]
            self.save_conversation(
                user_id,
                group_id,
                result["conversation_id"],
                query,
                result["response"],
                intent
            )
            return intent
        except Exception as e:
            error_msg = f"意图识别失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise ChatFlowIntentRecognitionError(error_msg)

    def _validate_intent(self, intent: Dict) -> Tuple[Dict, str]:
        """验证意图识别结果"""
        is_valid = "success"
        if not intent or not isinstance(intent, dict):
            self.logger.warning(f"意图格式无效: {json.dumps(intent, ensure_ascii=False)}, 使用默认意图")
            return INTENT_VALIDATION["default_intent"], "fail"
        intent_type = intent.get("intent_type")
        if not intent_type or intent_type not in INTENT_VALIDATION["valid_intents"]:
            self.logger.warning(f"意图类型无效: {intent_type}")
            return INTENT_VALIDATION["default_intent"], "fail"
        self.logger.info(f"意图验证成功: {intent_type}")
        return intent, is_valid