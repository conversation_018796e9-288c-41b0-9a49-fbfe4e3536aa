#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatFlow服务，支持基于conversation_id的连续对话
"""
import sys
import os
import time
import uuid
import re
import json
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from loguru import logger
import asyncio
import redis
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from services.chat_storage_service import ConversationHistoryStorage
from config.logging_config import configure_logging

configure_logging()

class ConversationSession:
    """
    对话会话类，表示一个对话会话
    """
    def __init__(self, model_provider, request_id: str = None):
        self.model = model_provider
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        
    async def make_blocking_request(self, system_prompt: str, user_query: str, 
                               conversation_id: str = None, user_id: str = "abc-123") -> Union[Dict[str, Any], None]:
        """
        发送阻塞式请求
        
        Args:
            system_prompt: 系统提示
            user_query: 用户查询
            conversation_id: 对话ID
            user: 用户标识
            
        Returns:
            Dict: 响应结果
        """
        data = {
            "query": user_query,
            "response_mode": "blocking",
            "user_id": user_id,
            "inputs": {
                "system_prompt": system_prompt,
            },
            "conversation_id": conversation_id
        }
    
        self.logger.info(f"用户ID: {user_id}, 对话ID: {conversation_id}")
        
        response = await self.model.generate(
            sys_prompt=system_prompt, 
            query=user_query, 
            response_mode="blocking", 
            user_id=user_id,
            inputs=data["inputs"],
            conversation_id=conversation_id
        )
        conversation_id = response.get("conversation_id")
        meta_data = response.get("metadata")
        answer = response.get("answer")
        text_content = self._remove_think(answer)
        
        return {
            "response": text_content,
            "conversation_id": conversation_id,
            "model_usage": self._extract_model_usage(response),
        }
        
    def _remove_think(self, text_content: str) -> str:
        """移除<think>标签及其内容"""
        if not text_content:
            return ""
        pattern = r'<think>.*?</think>'
        new_text_content = re.sub(pattern, '', text_content, flags=re.DOTALL)
        return new_text_content

    def _extract_model_usage(self, response: dict) -> dict:
        """提取模型使用情况"""
        return {
            "total_tokens": response.get("metadata", {}).get("usage", {}).get("total_tokens", 0),
            "prompt_tokens": response.get("metadata", {}).get("usage", {}).get("prompt_tokens", 0),
            "completion_tokens": response.get("metadata", {}).get("usage", {}).get("completion_tokens", 0),
            "elapsed_time": response.get("metadata", {}).get("usage", {}).get("latency", 0),
            "created_at": datetime.utcfromtimestamp(response.get("created_at", 0)).isoformat()
        }

class ConversationManager:
    """对话管理类，负责管理多轮对话"""
    def __init__(self, conversation_session: ConversationSession, history_storage: ConversationHistoryStorage, request_id: str = None):
        """
        初始化对话管理器
        
        Args:
            conversation_session: 对话会话
            history_storage: 对话历史存储
        """
        self.conversation_session = conversation_session
        self.history_storage = history_storage
        self.conversation_timeout = history_storage.conversation_timeout
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
    def is_new_conversation(self, user_id: str, group_id: str) -> bool:
        """
        判断是否为新对话
        
        条件:
        1. Redis中不存在该用户的对话历史记录
        2. 若存在历史记录，则当前时间与上次对话时间差超过阈值
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            bool: 是否为新对话
        """
        # 检查是否存在对话历史
        if not self.history_storage.check_key_exists(user_id, group_id):
            return True
        
        # 获取最后更新时间
        _, last_time = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        # 检查对话是否超时
        if last_time:
            current_time = time.time()
            if current_time - last_time > self.conversation_timeout:
                # 对话已超时，清除历史
                self.history_storage.delete_conversation_data(user_id, group_id)
                return True
        
        return False

    def get_conversation_history(self, user_id: str, group_id: str) -> Tuple[Optional[str], List[Dict], Optional[Dict]]:
        """
        获取用户的对话历史
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            Tuple[Optional[str], List[Dict], Optional[Dict]]: 
                - 对话ID
                - 对话历史列表
                - 最后识别的意图
        """
        # 检查是否为新对话
        if self.is_new_conversation(user_id, group_id):
            return None, [], None
        
        # 获取原始对话数据
        conversation_data_str, _ = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        # 解析对话数据
        if conversation_data_str:
            data = json.loads(conversation_data_str)
            conversation_id = data.get("conversation_id")
            history = data.get("history", [])
            intent = data.get("intent", {})
            return conversation_id, history, intent
        
        return None, [], None
    
    def save_conversation_intent(self, user_id: str, group_id: str, conversation_id: str, 
                        user_query: str, response: str, intent: Optional[Dict] = None) -> None:
        """
        添加新的对话轮次到历史记录中
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            conversation_id: 对话ID
            user_query: 用户查询
            response: 系统回复
            intent: 本轮对话的意图
        """
        # 获取现有历史
        conversation_id_existing, history, existing_intent = self.get_conversation_history(user_id, group_id)
        
        # 如果是新对话或对话ID不同，则清空历史
        if conversation_id_existing != conversation_id:
            history = []
        
        # 添加新的对话记录
        history.append({
            "user_query": user_query,
            "intent": intent,
            "response": response,
            "timestamp": time.time()
        })
        
        # 保存对话数据
        conversation_data = {
            "conversation_id": conversation_id,
            "history": history,
            "intent": intent
        }
        self.logger.info(f"保存对话: 用户ID: {user_id}, 组织ID: {group_id}, 对话历史: {json.dumps(history, ensure_ascii=False)}")
        # 保存到Redis
        self.history_storage.save_raw_conversation_data(user_id, group_id, json.dumps(conversation_data))

    def update_conversation_intent(self, user_id: str, group_id: str, intent: Dict) -> None:
        """
        更新对话的最新意图
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            intent: 新的意图
        """
        # 获取原始对话数据
        conversation_data_str, _ = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        if conversation_data_str:
            # 解析数据
            data = json.loads(conversation_data_str)
            data["intent"] = intent
            
            # 保存更新后的数据
            self.history_storage.save_raw_conversation_data(user_id, group_id, json.dumps(data))
            self.logger.info(f"已更新对话意图: 用户ID: {user_id}, 组织ID: {group_id} -> {intent}")
    
    def clear_conversation(self, user_id: str, group_id: str) -> None:
        """
        清除用户的对话历史
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
        """
        self.history_storage.delete_conversation_data(user_id, group_id)
        self.logger.info(f"清除用户的对话历史，用户ID: {user_id}, 组织ID: {group_id}")
    
    async def process_message_intent(self, system_prompt: str, user_query: str, 
                                        user_id: str, group_id: str, intent: Optional[Dict] = None) -> Dict:
        """
        处理用户消息并保存对话历史和意图
        
        Args:
            system_prompt: 系统提示
            user_query: 用户原始查询（不包含历史）
            user_id: 用户ID
            group_id: 组织ID
            intent: 识别的意图
            
        Returns:
            Dict: 处理结果
        """
        # 获取对话历史
        conversation_id, history, existing_intent = self.get_conversation_history(user_id, group_id)
        
        # 发送请求
        result = await self.conversation_session.make_blocking_request(
            system_prompt, 
            user_query, 
            conversation_id,
            user_id
        )

        if result:
            response = result["response"]
            new_conversation_id = result["conversation_id"]
            intent_extraction = await self.intent_extraction(response)
            
            # 保存对话历史（只保存原始查询，不包含历史）
            self.save_conversation_intent(
                user_id, 
                group_id, 
                new_conversation_id, 
                user_query, 
                response,
                intent_extraction
            )
            
            # 返回结果
            return {
                "response": response,
                "conversation_id": new_conversation_id,
                "is_new_conversation": conversation_id != new_conversation_id,
                "model_usage": result["model_usage"],
                "intent": intent_extraction
            }
        else:
            self.logger.error("请求失败")
            return {
                "response": "请求处理失败，请稍后重试",
                "conversation_id": None,
                "is_new_conversation": True,
                "model_usage": {},
                "intent": intent
            }

    async def intent_extraction(self, intent) -> Dict:
        """
        提取意图
        """
        if not intent:
            return None
            
        # 移除JSON标记
        clean_text = intent.replace("```json", "").replace("```", "").strip()
        
        try:
            # 尝试解析JSON
            data = json.loads(clean_text)
            return data
        except Exception as e:
            self.logger.warning(f"解析意图失败: {e}")
        
        return None
