#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
llm 模型服务
"""
import sys
import os
import time
import uuid
import re
import json
from typing import Dict, List, Optional, Any, Tuple, Union
from loguru import logger
import redis

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from services.chat_storage_service import ConversationHistoryStorage
from config.logging_config import configure_logging

configure_logging()

class ModelService:
    
    def __init__(self, model_provider, request_id: str = None):
        self.model = model_provider
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)


    async def make_blocking_request(self, messages: List[Dict], 
                                  conversation_id: str = None, 
                                  user_id: str = "abc-123") -> Union[Dict[str, Any], None]:
        """
        发送阻塞式请求
        
        Args:
            messages: 消息列表，包含系统提示、历史对话和当前查询
            conversation_id: 对话ID
            user_id: 用户标识
            
        Returns:
            Dict: 响应结果
        """
        # 如果没有conversation_id，生成一个新的
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
            
        self.logger.info(f"对话ID: {conversation_id}")
        # self.logger.info(f"会话消息: {json.dumps(messages, ensure_ascii=False)}")
        # 调用模型生成回复
        response = await self.model.generate(messages=messages)
        
        answer = response.choices[0].message.content
        model_extra = response.choices[0].message.reasoning_content
        model = response.model
        finish_reason = response.choices[0].finish_reason
        
        text_content = self._remove_think(answer)
        
        return {
            "response": text_content,
            "conversation_id": conversation_id,
            "model_usage": self._extract_model_usage(response),
        }
        
    def _remove_think(self, text_content: str) -> str:
        """移除<think>标签及其内容"""
        if not text_content:
            return ""
        pattern = r'<think>.*?</think>'
        new_text_content = re.sub(pattern, '', text_content, flags=re.DOTALL)
        return new_text_content

    def _extract_model_usage(self, response: dict) -> dict:
        """提取模型使用情况"""
        return {
            "total_tokens": response.usage.total_tokens,
            "prompt_tokens": response.usage.prompt_tokens,
            "completion_tokens": response.usage.completion_tokens,
            "elapsed_time": 0.0,
            "created_at": str(response.created)
        }

class ChatService:
    """聊天服务类，提供对外接口和会话管理"""
    def __init__(self, ModelService: ModelService, history_storage: ConversationHistoryStorage, request_id: str = None, max_conversation_rounds: int = 10):
        self.model_service = ModelService
        self.history_storage = history_storage
        self.conversation_timeout = history_storage.conversation_timeout
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        self.max_conversation_rounds = max_conversation_rounds

    def is_new_conversation(self, user_id: str, group_id: str) -> bool:
        """
        判断是否为新对话
        
        条件:
        1. Redis中不存在该用户的对话历史记录
        2. 若存在历史记录，则当前时间与上次对话时间差超过阈值
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            bool: 是否为新对话
        """
        # 检查是否存在对话历史
        if not self.history_storage.check_key_exists(user_id, group_id):
            return True
        
        # 获取最后更新时间
        _, last_time = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        # 检查对话是否超时
        if last_time:
            current_time = time.time()
            if current_time - last_time > self.conversation_timeout:
                # 对话已超时，清除历史
                self.history_storage.delete_conversation_data(user_id, group_id)
                return True
        
        return False

    def get_conversation_history(self, user_id: str, group_id: str) -> Tuple[Optional[str], List[Dict], Optional[Dict]]:
        """
        获取用户的对话历史
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            Tuple[Optional[str], List[Dict], Optional[Dict]]: 
                - 对话ID
                - 对话历史列表
                - 最后识别的意图
        """
        # 检查是否为新对话
        if self.is_new_conversation(user_id, group_id):
            return None, [], None
        
        # 获取原始对话数据
        conversation_data_str, _ = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        # 解析对话数据
        if conversation_data_str:
            data = json.loads(conversation_data_str)
            conversation_id = data.get("conversation_id")
            history = data.get("history", [])
            intent = data.get("intent", {})
            return conversation_id, history, intent
        
        return None, [], None
    
    def save_conversation_intent(self, user_id: str, group_id: str, conversation_id: str, 
                        user_query: str, response: str, intent: Optional[Dict] = None) -> None:
        """
        添加新的对话轮次到历史记录中
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            conversation_id: 对话ID
            user_query: 用户查询
            response: 系统回复
            intent: 识别的意图
        """
        # 获取现有历史
        conversation_id_existing, history, existing_intent = self.get_conversation_history(user_id, group_id)
        
        # 如果是新对话或对话ID不同，则清空历史
        if conversation_id_existing != conversation_id:
            history = []
        
        # 添加新的对话记录
        history.append({
            "user_query": user_query,
            "intent": intent,
            "response": response,
            "timestamp": time.time()
        })
        
        # 保存对话数据
        conversation_data = {
            "conversation_id": conversation_id,
            "history": history,
            "intent": intent
        }
        self.logger.info(f"保存对话: {user_id}@{group_id} -> {json.dumps(conversation_data, ensure_ascii=False)}")
        # 保存到Redis
        self.history_storage.save_raw_conversation_data(user_id, group_id, json.dumps(conversation_data))
    
    def update_conversation_intent(self, user_id: str, group_id: str, intent: Dict) -> None:
        """
        更新对话的最新意图
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            intent: 新的意图
        """
        # 获取原始对话数据
        conversation_data_str, _ = self.history_storage.get_raw_conversation_data(user_id, group_id)
        
        if conversation_data_str:
            # 解析数据
            data = json.loads(conversation_data_str)
            data["intent"] = intent
            
            # 保存更新后的数据
            self.history_storage.save_raw_conversation_data(user_id, group_id, json.dumps(data))
            self.logger.info(f"已更新对话意图: {user_id}@{group_id} -> {intent}")
    
    def clear_conversation(self, user_id: str, group_id: str) -> None:
        """
        清除用户的对话历史
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
        """
        self.history_storage.delete_conversation_data(user_id, group_id)
        self.logger.info(f"会话已结束，用户ID: {user_id}, 组织ID: {group_id}")
    
    def build_messages(self, system_prompt: str, user_query: str, history: List[Dict]) -> List[Dict]:
        """
        构建消息列表
        
        Args:
            system_prompt: 系统提示
            user_query: 用户查询
            history: 对话历史
            
        Returns:
            List[Dict]: 消息列表
        """
        messages = []
        
        # 添加系统消息
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史对话，只保留最近的N轮对话
        if history:
            history = history[-self.max_conversation_rounds:]
            for item in history:
                if "user_query" in item and item["user_query"]:
                    messages.append({"role": "user", "content": item["user_query"]})
                if "response" in item and item["response"]:
                    messages.append({"role": "assistant", "content": item["response"]})
        
        # 添加当前用户查询
        messages.append({"role": "user", "content": user_query})
        
        return messages
    
    async def process_message_intent(self, system_prompt: str, user_query: str, 
                  user_id: str, group_id: str, intent: Optional[Dict] = None) -> Dict:
        """
        聊天接口
        
        Args:
            system_prompt: 系统提示
            user_query: 用户查询
            user_id: 用户ID
            group_id: 组织ID
            intent: 识别的意图
            
        Returns:
            Dict: 处理结果
        """
        # 获取对话历史
        conversation_id, history, existing_intent = self.get_conversation_history(user_id, group_id)
        
        # 构建消息列表
        messages = self.build_messages(system_prompt, user_query, history)
        self.logger.debug(f"消息列表: {messages}")
        # 发送请求
        result = await self.model_service.make_blocking_request(
            messages,
            conversation_id,
            user_id
        )
        self.logger.info(f"请求结果: {result}")
        if result:
            response = result["response"]
            new_conversation_id = result["conversation_id"]
            intent_extraction = await self.intent_extraction(response)
            
            # 保存对话历史（只保存原始查询，不包含历史）
            self.save_conversation_intent(
                user_id, 
                group_id, 
                new_conversation_id, 
                user_query,  # 原始查询
                response,
                intent_extraction
            )
            
            # 返回结果
            return {
                "response": response,
                "conversation_id": new_conversation_id,
                "is_new_conversation": conversation_id != new_conversation_id,
                "model_usage": result["model_usage"],
                "intent": intent_extraction
            }
        else:
            self.logger.error("请求失败")
            return {
                "response": "请求处理失败，请稍后重试",
                "conversation_id": None,
                "is_new_conversation": True,
                "model_usage": {},
                "intent": intent
            }

    async def intent_extraction(self, intent) -> Dict:
        """
        提取意图
        """
        if not intent:
            return None
            
        # 移除JSON标记
        self.logger.debug(f"clean_text: {intent}")
        clean_text = intent.replace("```json", "").replace("```", "").replace("\n\n", "").strip()
        self.logger.debug(f"意图: {clean_text}")
        try:
            # 尝试解析JSON
            data = json.loads(clean_text)
            return data
        except Exception as e:
            self.logger.warning(f"解析意图失败: {e}")
        
        return None
