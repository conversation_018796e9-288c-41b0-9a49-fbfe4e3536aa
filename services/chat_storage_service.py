"""
对话历史存储服务，负责多轮对话的历史记录管理。
"""
import time
import json
from typing import Dict, List, Optional, Tuple
import redis
from redis import ConnectionPool
from loguru import logger
from config.redis_config import get_redis_connection_params

class ConversationHistoryStorage:
    """对话历史存储类，负责对话历史的基础存储与查询操作"""
    
    def __init__(self, redis_client: Optional[redis.StrictRedis] = None, conversation_timeout: int = 600):
        """
        初始化对话历史存储
        
        Args:
            redis_client: Redis客户端实例，如果为None则自动创建
            conversation_timeout: 对话超时时间（秒），默认10分钟
        """
        if redis_client is None:
            # 使用配置文件中的参数创建Redis客户端
            redis_params = get_redis_connection_params()
            # self.pool = ConnectionPool(**redis_params)  # 使用ConnectionPool而不是Redis inf
            try:
                self.redis = redis.StrictRedis(**redis_params)
            except Exception as e:
                logger.error(f"无法创建Redis连接: {e}")
            logger.info(f"已创建Redis连接: {redis_params['host']}:{redis_params['port']}")
        else:
            self.redis = redis_client
        self.conversation_timeout = conversation_timeout
    
    def _get_conversation_key(self, user_id: str, group_id: str) -> str:
        """
        生成对话历史的Redis键
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            str: Redis键
        """
        return f"conversation:{group_id}:{user_id}"
    
    def _get_conversation_time_key(self, user_id: str, group_id: str) -> str:
        """
        生成对话最后更新时间的Redis键
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            str: Redis键
        """
        return f"conversation_time:{group_id}:{user_id}"
    
    def get_conversation_intent_key(self, user_id: str, group_id: str) -> str:
        """
        生成对话意图缓存的Redis键
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            str: Redis键
        """
        return f"conversation_intent:{group_id}:{user_id}"
    
    def get_raw_conversation_data(self, user_id: str, group_id: str) -> Tuple[Optional[str], Optional[float]]:
        """
        获取原始对话数据和最后更新时间
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            Tuple[Optional[str], Optional[float]]: 
                - 原始对话数据JSON字符串
                - 最后更新时间戳
        """
        key = self._get_conversation_key(user_id, group_id)
        time_key = self._get_conversation_time_key(user_id, group_id)
        
        conversation_data = self.redis.get(key)
        last_time_str = self.redis.get(time_key)
        last_time = float(last_time_str) if last_time_str else None
        
        return conversation_data, last_time
    
    def get_conversation_intent(self, user_id: str, group_id: str) -> Optional[str]:
        """
        获取对话意图缓存

        Args:
            user_id: 用户ID
            group_id: 组织ID

        Returns:
            Optional[Dict]: 对话意图数据
        """
        key = self.get_conversation_intent_key(user_id, group_id)
        time_key = self._get_conversation_time_key(user_id, group_id)
        
        conversation_intent = self.redis.get(key)
        last_time_str = self.redis.get(time_key)
        last_time = float(last_time_str) if last_time_str else None
        
        return conversation_intent, last_time
    
    def save_raw_conversation_data(self, user_id: str, group_id: str, conversation_data: str) -> None:
        """
        保存原始对话数据
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            conversation_data: 对话数据JSON字符串
        """
        key = self._get_conversation_key(user_id, group_id)
        time_key = self._get_conversation_time_key(user_id, group_id)
        
        # 保存到Redis
        self.redis.set(key, conversation_data)
        self.redis.set(time_key, time.time())
        
        # 设置过期时间（比超时时间长一些，以便于调试）
        expiry = self.conversation_timeout * 2
        self.redis.expire(key, expiry)
        self.redis.expire(time_key, expiry)
    
    def save_conversation_intent(self, user_id: str, group_id: str, conversation_intent: str) -> None:
        """
        保存对话意图
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            intent: 意图数据
        """
        key = self.get_conversation_intent_key(user_id, group_id)
        time_key = self._get_conversation_time_key(user_id, group_id)
        self.redis.set(key, conversation_intent)
        self.redis.set(time_key, time.time())
        
        expiry = self.conversation_timeout * 2
        self.redis.expire(key, expiry)
    
    
    def delete_conversation_data(self, user_id: str, group_id: str) -> None:
        """
        删除对话数据
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
        """
        key = self._get_conversation_key(user_id, group_id)
        time_key = self._get_conversation_time_key(user_id, group_id)
        intent_key = self.get_conversation_intent_key(user_id, group_id)
        
        self.redis.delete(key)
        self.redis.delete(time_key)
        self.redis.delete(intent_key)
    
    def check_key_exists(self, user_id: str, group_id: str) -> bool:
        """
        检查对话键是否存在
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            bool: 是否存在
        """
        key = self._get_conversation_key(user_id, group_id)
        return bool(self.redis.exists(key))
    
    def check_intent_key_exists(self, user_id: str, group_id: str) -> bool:
        """
        检查意图键是否存在
        
        Args:
            user_id: 用户ID
            group_id: 组织ID
            
        Returns:
            bool: 是否存在
        """
        key = self.get_conversation_intent_key(user_id, group_id)
        return bool(self.redis.exists(key))