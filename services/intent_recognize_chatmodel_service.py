#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""chatmodel意图识别服务，支持连续对话意图识别。"""
import sys
import os
import json
import time
import uuid
import re
from typing import Dict, List, Optional, Tuple, Union, Any
from loguru import logger
import logging
import redis
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from services.chat_storage_service import ConversationHistoryStorage
from prompts.intent_prompt import system_prompt_1, user_query_1, system_prompt_multi_turn, user_query_multi_turn
from config.intent_config import INTENT_MODEL_CONFIG, INTENT_VALIDATION, INTENT_CACHE
from config.chat_config import CONVERSATION_TIMEOUT
from config.intent_config import MAX_INTENT_CONVERSATION_ROUNDS


class ChatModelIntentRecognitionError(Exception):
    """意图识别相关的自定义异常"""
    pass

class ChatModelBaseIntentService:
    """基础意图识别服务"""

    def __init__(self, model_provider, request_id: str = None):
        self.model = model_provider
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
    async def generate_intent(self, messages: List[Dict], conversation_id: str = None) -> Dict[str, Any]:
        """生成意图识别结果"""
        if not conversation_id:
            conversation_id = str(uuid.uuid4())
        try:
            response = await self.model.generate(messages=messages)
            answer = response.choices[0].message.content
            text_content = self._remove_think(answer)
            intent = await self._extract_intent(text_content)
            
            return {
                "response": text_content,
                "intent": intent,
                "conversation_id": conversation_id,
                "model_usage": self._extract_model_usage(response)
            }
        except Exception as e:
            self.logger.error(f"意图识别失败: {str(e)}")
            raise ChatModelIntentRecognitionError(f"意图识别模型调用失败: {str(e)}")

    def _remove_think(self, text_content: str) -> str:
        """移除<think>标签及其内容"""
        if not text_content:
            return ""
        pattern = r'<think>.*?</think>'
        new_text_content = re.sub(pattern, '', text_content, flags=re.DOTALL)
        return new_text_content
    def _extract_model_usage(self, response: Any) -> Dict:
        """提取模型使用情况"""
        return {
            "total_tokens": response.usage.total_tokens,
            "prompt_tokens": response.usage.prompt_tokens,
            "completion_tokens": response.usage.completion_tokens,
            "elapsed_time": 0.0,
            "created_at": str(response.created)
        }

    async def _extract_intent(self, text: str) -> Optional[Dict]:
        """从响应文本中提取意图JSON"""
        if not text:
            return None
        clean_text = text.replace("```json", "").replace("```", "").strip()
        try:
            return json.loads(clean_text)
        except Exception as e:
            self.logger.warning(f"意图JSON解析失败: {str(e)}")
            return None



class ChatModelIntentRecognizer:
    """意图识别类，支持多轮对话管理+意图识别"""

    def __init__(self, redis_client: Optional[redis.StrictRedis] = None, request_id: str = None):
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        self.conversation_timeout = CONVERSATION_TIMEOUT
        self.max_intent_conversation_rounds = MAX_INTENT_CONVERSATION_ROUNDS
        # 初始化对话历史存储
        self.history_storage = ConversationHistoryStorage(redis_client, self.conversation_timeout)
        
        # 初始化模型服务
        llm_provider = get_llm_provider(INTENT_MODEL_CONFIG["model_id"])
        self.model_service = ChatModelBaseIntentService(llm_provider, request_id)

        # 缓存设置
        self.cache_enabled = INTENT_CACHE["enabled"]
        self.cache_expiration = INTENT_CACHE["expiration"]
        
        self.logger.info(f"ChatModel意图识别器初始化完成: {request_id}")

    def is_new_conversation(self, user_id: str, group_id: str) -> bool:
        """判断是否为新对话"""
        if not self.history_storage.check_intent_key_exists(user_id, group_id):
            return True
            
        _, last_time = self.history_storage.get_conversation_intent(user_id, group_id)
        if last_time and (time.time() - last_time > self.conversation_timeout):
            self.history_storage.delete_conversation_data(user_id, group_id)
            return True
            
        return False

    def get_conversation_history(self, user_id: str, group_id: str) -> Tuple[Optional[str], List[Dict], Optional[Dict]]:
        """获取对话历史"""
        if self.is_new_conversation(user_id, group_id):
            return None, [], None
            
        convesation_intent, _ = self.history_storage.get_conversation_intent(user_id, group_id)
        if convesation_intent:
            data = json.loads(convesation_intent)
            return (
                data.get("conversation_id"),
                data.get("history", []),
                data.get("intent", {})
            )
            
        return None, [], None

    def build_messages(self, system_prompt: str, user_query: str, history: List[Dict]) -> List[Dict]:
        """构建消息列表"""
        messages = [{"role": "system", "content": system_prompt}]
        # 添加历史对话，只保留最近的N轮对话
        if history:
            history = history[-self.max_intent_conversation_rounds:]
            for item in history:
                if "user_query" in item and item["user_query"]:
                    messages.append({"role": "user", "content": item["user_query"]})
                if "response" in item and item["response"]:
                    messages.append({"role": "assistant", "content": item["response"]})
        messages.append({"role": "user", "content": user_query})
        return messages

    def save_conversation(self, user_id: str, group_id: str, conversation_id: str, query: str, response: str, intent: Dict) -> None:
        """保存对话记录"""
        _, history, _ = self.get_conversation_history(user_id, group_id)
        
        history.append({
            "user_query": query,
            "intent": intent,
            "timestamp": time.time()
        })
        
        data = {
            "conversation_id": conversation_id,
            "history": history,
            "intent": intent
        }
        
        self.history_storage.save_conversation_intent(
            user_id, 
            group_id, 
            json.dumps(data)
        )

    async def recognize_intent(self, user_id: str, group_id: str, query: str) -> Dict:
        """识别用户查询的意图"""
        try:
            # 获取对话历史
            is_new = self.is_new_conversation(user_id, group_id)
            conversation_id, history, previous_intent = self.get_conversation_history(user_id, group_id)
            
            # 选择合适的提示词
            system_prompt = system_prompt_1 if is_new else system_prompt_multi_turn
            
            # 构建消息列表
            messages = self.build_messages(system_prompt, query, history)
            
            # 调用模型服务
            result = await self.model_service.generate_intent(messages, conversation_id)
            
            # 验证意图
            intent = result["intent"]
            # validated_intent, status = self._validate_intent(intent)
            
            # 保存对话记录
            self.save_conversation(
                user_id, 
                group_id,
                result["conversation_id"],
                query,
                result["response"],
                intent
            )
            
            # 返回结果
            return intent
            
        except Exception as e:
            error_msg = f"意图识别失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise ChatModelIntentRecognitionError(error_msg)

    def _validate_intent(self, intent: Dict) -> Tuple[Dict, str]:
        """验证意图识别结果"""
        is_valid = "success"
        
        if not intent or not isinstance(intent, dict):
            self.logger.warning(f"意图格式无效: {json.dumps(intent, ensure_ascii=False)}, 使用默认意图")
            return INTENT_VALIDATION["default_intent"], "fail"
        
        # 获取意图类型
        intent_type = intent.get("intent_type")
        
        # 验证意图类型
        if not intent_type or intent_type not in INTENT_VALIDATION["valid_intents"]:
            self.logger.warning(f"意图类型无效: {intent_type}")
            return INTENT_VALIDATION["default_intent"], "fail"
        
        self.logger.info(f"意图验证成功: {intent_type}")
        return intent, is_valid