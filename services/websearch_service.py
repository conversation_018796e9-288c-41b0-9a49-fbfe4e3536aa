#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联网搜索服务
调用小米内部的websearch API进行联网搜索
"""

import httpx
import json
import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class WebSearchService:
    """联网搜索服务"""
    
    def __init__(self):
        """初始化websearch服务"""
        self.base_url = os.getenv('WEBSEARCH_BASE_URL', "")
        websearch_token = os.getenv('WEBSEARCH_TOKEN', '')
        self.headers = {
            'Authorization': f'Bearer {websearch_token}',
            'Content-Type': 'application/json'
        }
        self.timeout = 30.0
        
    async def search(self, query: str, user_id: str = "abc-123") -> Dict[str, Any]:
        """
        执行联网搜索
        
        Args:
            query: 搜索查询
            user_id: 用户ID
            
        Returns:
            搜索结果字典，包含webPages等信息
        """
        try:
            logger.info(f"开始联网搜索: {query}")
            
            # 构建请求数据
            payload = {
                "inputs": {"text": query},
                "response_mode": "streaming",
                "user": user_id
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    # 记录原始响应用于调试
                    response_text = response.text
                    logger.info(f"API响应状态码: {response.status_code}")
                    logger.info(f"API响应内容长度: {len(response_text)}")
                    logger.info(f"API响应前500字符: {response_text[:500]}")
                    
                    # 解析流式响应
                    search_result = await self._parse_streaming_response(response)
                    logger.info(f"联网搜索成功，获取到 {len(search_result.get('webPages', {}).get('value', []))} 个结果")
                    return search_result
                else:
                    logger.error(f"联网搜索失败，状态码: {response.status_code}, 响应: {response.text}")
                    return {"error": f"搜索失败: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"联网搜索异常: {str(e)}")
            return {"error": f"搜索异常: {str(e)}"}
    
    async def _parse_streaming_response(self, response: httpx.Response) -> Dict[str, Any]:
        """
        解析流式响应
        
        Args:
            response: HTTP响应对象
            
        Returns:
            解析后的搜索结果
        """
        try:
            # 读取响应内容
            content = response.text
            logger.info(f"开始解析响应内容，长度: {len(content)}")
            
            # 尝试多种解析方式
            
            # 方式1: 直接解析整个响应为JSON
            try:
                data = json.loads(content)
                logger.info(f"直接JSON解析成功，数据类型: {type(data)}")
                
                # 检查不同的数据结构
                if isinstance(data, dict):
                    # 直接包含webPages的情况
                    if 'webPages' in data:
                        logger.info("找到webPages字段")
                        return data
                    
                    # 包含data字段的情况
                    if 'data' in data and isinstance(data['data'], dict) and 'webPages' in data['data']:
                        logger.info("在data字段中找到webPages")
                        return data['data']
                    
                    # 包含json字段的情况
                    if 'json' in data:
                        json_data = data['json']
                        if isinstance(json_data, list) and len(json_data) > 0:
                            result = json_data[0]
                            if isinstance(result, dict) and 'data' in result:
                                logger.info("在json数组的第一个元素的data字段中查找")
                                return result['data']
                
                # 如果是数组
                elif isinstance(data, list) and len(data) > 0:
                    first_item = data[0]
                    if isinstance(first_item, dict):
                        if 'webPages' in first_item:
                            return first_item
                        if 'data' in first_item and 'webPages' in first_item['data']:
                            return first_item['data']
                            
            except json.JSONDecodeError as e:
                logger.info(f"直接JSON解析失败: {str(e)}")
            
            # 方式2: 按行解析流式响应
            lines = content.strip().split('\n')
            logger.info(f"按行解析，共 {len(lines)} 行")
            
            for i, line in enumerate(lines):
                if line.strip():
                    try:
                        # 处理 'data: ' 前缀
                        if line.startswith('data: '):
                            json_str = line[6:]
                        elif line.startswith('data:'):
                            json_str = line[5:]
                        else:
                            json_str = line
                            
                        data = json.loads(json_str)
                        # logger.info(f"第 {i+1} 行解析成功，数据类型: {type(data)}")
                        
                        # 检查是否包含搜索结果
                        if isinstance(data, dict):
                            # 检查event类型，寻找包含最终结果的事件
                            event_type = data.get('event', '')
                            # logger.info(f"第 {i+1} 行事件类型: {event_type}")
                            
                            # 直接包含webPages的情况
                            if 'webPages' in data:
                                # logger.info(f"在第 {i+1} 行找到webPages")
                                return data
                            
                            # 检查data字段中是否有webPages
                            if 'data' in data:
                                data_content = data['data']
                                if isinstance(data_content, dict):
                                    if 'webPages' in data_content:
                                        # logger.info(f"在第 {i+1} 行的data字段中找到webPages")
                                        return data_content
                                    
                                    # 检查是否是工作流完成事件，包含outputs
                                    if 'outputs' in data_content and isinstance(data_content['outputs'], dict):
                                        outputs = data_content['outputs']
                                        if 'json' in outputs:
                                            json_data = outputs['json']
                                            if isinstance(json_data, list) and len(json_data) > 0:
                                                result = json_data[0]
                                                if isinstance(result, dict) and 'data' in result:
                                                    logger.info(f"在第 {i+1} 行的outputs.json中找到数据")
                                                    return result['data']
                            
                            # 检查json字段
                            if 'json' in data:
                                json_data = data['json']
                                if isinstance(json_data, list) and len(json_data) > 0:
                                    result = json_data[0]
                                    if isinstance(result, dict) and 'data' in result:
                                        logger.info(f"在第 {i+1} 行的json数组中找到数据")
                                        return result['data']
                                        
                    except json.JSONDecodeError as e:
                        logger.debug(f"第 {i+1} 行JSON解析失败: {str(e)}")
                        continue
            
            # 方式3: 查找包含特定关键词的内容
            if 'webPages' in content:
                logger.info("响应中包含webPages关键词，但无法正确解析JSON结构")
                # 尝试提取webPages部分
                try:
                    start_idx = content.find('"webPages"')
                    if start_idx != -1:
                        # 找到一个可能的JSON片段
                        logger.info("尝试从webPages关键词位置开始解析")
                except:
                    pass
                
            logger.warning("所有解析方式都失败了")
            return {"error": "无法解析搜索响应"}
            
        except Exception as e:
            logger.error(f"解析搜索响应异常: {str(e)}")
            return {"error": f"解析响应异常: {str(e)}"}
    
    def extract_summaries(self, search_result: Dict[str, Any]) -> List[str]:
        """
        从搜索结果中提取summary信息
        
        Args:
            search_result: 搜索结果字典
            
        Returns:
            summary列表
        """
        summaries = []
        
        try:
            web_pages = search_result.get('webPages', {})
            if 'value' in web_pages:
                for page in web_pages['value']:
                    if 'summary' in page and page['summary']:
                        summaries.append(page['summary'])
                        
            logger.info(f"提取到 {len(summaries)} 个summary")
            
        except Exception as e:
            logger.error(f"提取summary异常: {str(e)}")
            
        return summaries
    
    def format_results(self, search_result: Dict[str, Any]) -> List[str]:
        """
        从搜索结果中提取summary信息
        
        Args:
            search_result: 搜索结果字典
            
        Returns:
            summary列表
        """
        try:
            formated_docs = []
            web_pages = search_result.get('webPages', {})
            if 'value' in web_pages:
                for page in web_pages['value']:
                    data = {
                    "title": "",
                    "content": "",
                    "docName": "",
                    "docUrl": "",
                    "sheetName": "",
                    "owner": "",
                    "update_time": "",
                    "publish_time": "",
                    "doc_type": "",
                    "project_area": "",
                    "siteIcon": "",
                    "siteName": "",
                    
                    }
                    data.update({"title": page.get("name", "")})
                    data.update({"content": page.get("summary", "")})
                    data.update({"docName": page.get("name", "")})
                    data.update({"docUrl": page.get("displayUrl", "")})
                    data.update({"sheetName": page.get("siteName", "")})
                    data.update({"owner": page.get("provider", "")})
                    data.update({"update_time": page.get("datePublished", "")})
                    data.update({"publish_time": page.get("datePublished", "")})
                    data.update({"doc_type": page.get("doc_type", "webpage")})
                    data.update({"project_area": page.get("project_area", "")})
                    data.update({"siteIcon": page.get("siteIcon", "")})
                    data.update({"siteName": page.get("siteName", "")})
                    formated_docs.append(data)
                return formated_docs
                
        except Exception as e:
            logger.error(f"提取结果异常: {str(e)}")
        return []    
    
    def format_search_context(self, summaries: List[str], max_length: int = 3000) -> str:
        """
        格式化搜索上下文，用于LLM问答
        
        Args:
            summaries: summary列表
            max_length: 最大长度限制
            
        Returns:
            格式化后的搜索上下文
        """
        if not summaries:
            return ""
            
        # 构建搜索上下文
        context_parts = []
        current_length = 0
        
        for i, summary in enumerate(summaries, 1):
            formatted_summary = f"【搜索结果 {i}】\n{summary}\n"
            
            # 检查长度限制
            if current_length + len(formatted_summary) > max_length:
                break
                
            context_parts.append(formatted_summary)
            current_length += len(formatted_summary)
        
        if context_parts:
            context = "以下是联网搜索到的相关信息：\n\n" + "\n".join(context_parts)
            logger.info(f"格式化搜索上下文完成，长度: {len(context)}")
            return context
        else:
            return ""


# 全局websearch服务实例
_websearch_service = None

def get_websearch_service() -> WebSearchService:
    """获取websearch服务实例"""
    global _websearch_service
    if _websearch_service is None:
        _websearch_service = WebSearchService()
    return _websearch_service


# 测试功能
async def test_websearch():
    """测试websearch功能"""
    service = get_websearch_service()
    result = await service.search("刘德华是谁")
    
    print("搜索结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    if 'webPages' in result:
        summaries = service.extract_summaries(result)
        print(f"\n提取到 {len(summaries)} 个summary:")
        for i, summary in enumerate(summaries, 1):
            print(f"\n{i}. {summary[:200]}...")
        
        context = service.format_search_context(summaries)
        print(f"\n格式化上下文 (长度: {len(context)}):")
        print(context[:500] + "..." if len(context) > 500 else context)

        search_result = service.format_results(result)
        print(f"\n提取到 {len(search_result)} 个结果:")
        for i, page in enumerate(search_result, 1):
            print(f"\n{i}. {page}")

if __name__ == "__main__":
    asyncio.run(test_websearch())
