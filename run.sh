#!/bin/bash

# 设置工作目录
WORK_DIR="/home/<USER>/bin"
cd $WORK_DIR

# 创建日志目录
mkdir -p logs

# 检查端口是否被占用
check_port() {
    if lsof -i:8080 >/dev/null 2>&1; then
        echo "端口 8080 已被占用，请先释放端口"
        exit 1
    fi
}

# 启动应用函数
start_app() {
    check_port
    python -m uvicorn api.app:app --host 0.0.0.0 --port 8080 --workers 16 >logs/app.log 2>&1 &
    APP_PID=$!
    echo $APP_PID >logs/app.pid

    # 等待应用启动
    sleep 2
    if ! ps -p $APP_PID >/dev/null; then
        echo "应用启动失败，请检查日志: logs/app.log"
        exit 1
    fi
    echo "应用已在后台启动，PID: $APP_PID"
}

# 在后台启动应用
start_app

# 监控应用状态
echo "开始监控应用..."
while true; do
    if ps -p $APP_PID >/dev/null; then
        echo "[$(date)] 应用运行正常，PID: $APP_PID"
    else
        echo "[$(date)] 应用已停止，正在重启..."
        start_app
        echo "[$(date)] 应用已重启，新PID: $APP_PID"
    fi
    sleep 5
done
